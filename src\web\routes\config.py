#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理API路由

此模块提供系统配置相关的API接口，包括：
1. 交易所配置管理
2. 交易参数配置
3. 风险参数配置
4. 交易对选择配置
5. AI引擎配置
"""

import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import ccxt
import asyncio

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.logger import get_logger
from src.data.database_manager import DatabaseManager
from src.data.models import ExchangeConfig, TradingParameters, RiskParameters, AIConfig

logger = get_logger(__name__)

router = APIRouter()

# 数据库管理器实例
db_manager = DatabaseManager()

# Pydantic模型用于API请求验证
class ExchangeConfigRequest(BaseModel):
    # 支持前端的驼峰命名和后端的下划线命名
    exchange_name: Optional[str] = Field(None, description="交易所名称")
    exchangeName: Optional[str] = Field(None, description="交易所名称（前端字段）")
    api_key: Optional[str] = Field(None, description="API密钥")
    apiKey: Optional[str] = Field(None, description="API密钥（前端字段）")
    secret_key: Optional[str] = Field(None, description="密钥")
    secretKey: Optional[str] = Field(None, description="密钥（前端字段）")
    passphrase: Optional[str] = Field(None, description="密码短语（OKX需要）")
    sandbox_mode: Optional[bool] = Field(None, description="是否为沙盒模式")
    sandboxMode: Optional[bool] = Field(None, description="是否为沙盒模式（前端字段）")

    def get_exchange_name(self) -> str:
        """获取交易所名称，优先使用下划线命名"""
        return self.exchange_name or self.exchangeName or ""

    def get_api_key(self) -> str:
        """获取API密钥，优先使用下划线命名"""
        return self.api_key or self.apiKey or ""

    def get_secret_key(self) -> str:
        """获取密钥，优先使用下划线命名"""
        return self.secret_key or self.secretKey or ""

    def get_sandbox_mode(self) -> bool:
        """获取沙盒模式，优先使用下划线命名"""
        if self.sandbox_mode is not None:
            return self.sandbox_mode
        if self.sandboxMode is not None:
            return self.sandboxMode
        return True  # 默认为沙盒模式

class TradingParametersRequest(BaseModel):
    max_leverage: int = Field(10, ge=1, le=100, description="最大杠杆倍数")
    max_position_ratio: float = Field(0.5, ge=0.1, le=1.0, description="最大仓位比例")
    opening_confidence_threshold: int = Field(70, ge=0, le=100, description="开仓置信度阈值")
    position_confidence_threshold: int = Field(60, ge=0, le=100, description="持仓置信度阈值")
    default_stop_loss_percentage: float = Field(0.05, ge=0.01, le=0.5, description="默认止损百分比")
    default_take_profit_percentage: float = Field(0.1, ge=0.01, le=1.0, description="默认止盈百分比")

class RiskParametersRequest(BaseModel):
    max_leverage: int = Field(20, ge=1, le=100, description="最大杠杆")
    max_position_ratio: float = Field(0.8, ge=0.1, le=1.0, description="最大仓位比例")
    min_balance_threshold: float = Field(100, ge=10, description="最小余额阈值")
    opening_confidence_threshold: int = Field(70, ge=0, le=100, description="开仓置信度阈值")
    position_confidence_threshold: int = Field(60, ge=0, le=100, description="持仓置信度阈值")

class SymbolsRequest(BaseModel):
    symbols: List[str] = Field(..., description="交易对列表")

class AISettingsRequest(BaseModel):
    deepseek_api_key: str = Field(..., description="DeepSeek API密钥")
    analysis_interval: int = Field(300, ge=30, le=3600, description="分析间隔（秒）")
    lookback_period: int = Field(24, ge=1, le=168, description="数据回看周期（小时）")
    enable_opening_engine: bool = Field(True, description="启用AI开仓引擎")
    enable_position_engine: bool = Field(True, description="启用AI持仓引擎")


@router.get("/exchange")
async def get_exchange_config():
    """获取交易所配置"""
    try:
        config = db_manager.load_exchange_config()
        
        if not config:
            return JSONResponse(content={
                "success": True,
                "data": {
                    "exchange_name": "",
                    "api_key": "",
                    "secret_key": "",
                    "passphrase": "",
                    "sandbox_mode": True,
                    "configured": False
                }
            })
        
        # 隐藏敏感信息
        safe_config = {
            "exchange_name": config.exchange_name or "",
            "api_key": config.api_key[:8] + "..." if config.api_key else "",
            "secret_key": "***" if config.secret_key else "",
            "passphrase": "***" if config.passphrase else "",
            "sandbox_mode": config.sandbox_mode,
            "configured": True
        }

        return JSONResponse(content={"success": True, "data": safe_config})
        
    except Exception as e:
        logger.error(f"获取交易所配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/exchange")
async def save_exchange_config(config: ExchangeConfigRequest):
    """保存交易所配置"""
    try:
        # 获取标准化的字段值
        exchange_name = config.get_exchange_name()
        api_key = config.get_api_key()
        secret_key = config.get_secret_key()
        sandbox_mode = config.get_sandbox_mode()

        # 验证必填字段
        if not exchange_name:
            raise HTTPException(status_code=400, detail="交易所名称不能为空")
        if not api_key:
            raise HTTPException(status_code=400, detail="API密钥不能为空")
        if not secret_key:
            raise HTTPException(status_code=400, detail="密钥不能为空")

        # 验证交易所名称
        supported_exchanges = ["okx", "binance", "huobi", "bybit"]
        if exchange_name.lower() not in supported_exchanges:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的交易所: {exchange_name}。支持的交易所: {', '.join(supported_exchanges)}"
            )

        # 创建ExchangeConfig对象
        exchange_config = ExchangeConfig(
            exchange_name=exchange_name.lower(),
            api_key=api_key,
            secret_key=secret_key,
            passphrase=config.passphrase,
            sandbox_mode=sandbox_mode
        )

        success = db_manager.save_exchange_config(exchange_config)
        
        if not success:
            raise HTTPException(status_code=500, detail="保存交易所配置失败")
        
        logger.info(f"交易所配置已保存: {exchange_name}")

        return JSONResponse(content={
            "success": True,
            "message": "交易所配置保存成功",
            "data": {
                "exchange_name": exchange_name,
                "sandbox_mode": sandbox_mode
            }
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"保存交易所配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/trading")
async def get_trading_parameters():
    """获取交易参数"""
    try:
        params = db_manager.load_trading_parameters()

        if not params:
            # 返回默认参数
            default_params = {
                "max_leverage": 10,
                "max_position_ratio": 0.5,
                "opening_confidence_threshold": 70,
                "position_confidence_threshold": 60,
                "default_stop_loss_percentage": 0.05,
                "default_take_profit_percentage": 0.1
            }
            return JSONResponse(content={"success": True, "data": default_params})

        return JSONResponse(content={"success": True, "data": params})

    except Exception as e:
        logger.error(f"获取交易参数失败: {e}")
        return JSONResponse(content={"success": False, "message": str(e)}, status_code=500)


@router.post("/trading")
async def save_trading_parameters(params: TradingParametersRequest):
    """保存交易参数"""
    try:
        params_dict = params.model_dump()

        success = db_manager.save_trading_parameters(params_dict)

        if not success:
            return JSONResponse(content={"success": False, "message": "保存交易参数失败"}, status_code=500)

        logger.info("交易参数已保存")

        return JSONResponse(content={
            "success": True,
            "message": "交易参数保存成功",
            "data": params_dict
        })

    except Exception as e:
        logger.error(f"保存交易参数失败: {e}")
        return JSONResponse(content={"success": False, "message": str(e)}, status_code=500)


@router.get("/risk")
async def get_risk_parameters():
    """获取风险参数"""
    try:
        params = db_manager.load_risk_parameters()

        if not params:
            # 返回默认参数
            default_params = {
                "max_leverage": 20,
                "max_position_ratio": 0.8,
                "min_balance_threshold": 100,
                "opening_confidence_threshold": 70,
                "position_confidence_threshold": 60
            }
            return JSONResponse(content={"success": True, "data": default_params})

        return JSONResponse(content={"success": True, "data": params})

    except Exception as e:
        logger.error(f"获取风险参数失败: {e}")
        return JSONResponse(content={"success": False, "message": str(e)}, status_code=500)


@router.post("/risk")
async def save_risk_parameters(params: RiskParametersRequest):
    """保存风险参数"""
    try:
        params_dict = params.model_dump()

        success = db_manager.save_risk_parameters(params_dict)

        if not success:
            return JSONResponse(content={"success": False, "message": "保存风险参数失败"}, status_code=500)

        logger.info("风险参数已保存")

        return JSONResponse(content={
            "success": True,
            "message": "风险参数保存成功",
            "data": params_dict
        })

    except Exception as e:
        logger.error(f"保存风险参数失败: {e}")
        return JSONResponse(content={"success": False, "message": str(e)}, status_code=500)


@router.get("/symbols")
async def get_selected_symbols():
    """获取选择的交易对"""
    try:
        logger.info("开始获取选择的交易对")

        # 确保数据库已初始化
        if not hasattr(db_manager, '_initialized') or not db_manager._initialized:
            logger.info("数据库未初始化，正在初始化...")
            db_manager.init_database()

        symbols = db_manager.load_selected_symbols()
        logger.info(f"成功获取交易对: {len(symbols) if symbols else 0} 个")

        return JSONResponse(content={
            "success": True,
            "data": {
                "symbols": symbols or [],
                "count": len(symbols) if symbols else 0
            }
        })

    except Exception as e:
        logger.error(f"获取交易对失败: {e}", exc_info=True)
        return JSONResponse(
            content={
                "success": False,
                "message": f"获取交易对失败: {str(e)}",
                "error_type": type(e).__name__
            },
            status_code=500
        )


@router.post("/symbols")
async def save_selected_symbols(request: SymbolsRequest):
    """保存选择的交易对"""
    try:
        # 验证交易对格式
        valid_symbols = []
        for symbol in request.symbols:
            if "/" in symbol and ":" in symbol:  # 永续合约格式：BTC/USDT:USDT
                valid_symbols.append(symbol.upper())
            else:
                logger.warning(f"无效的交易对格式: {symbol}")
        
        if not valid_symbols:
            raise HTTPException(status_code=400, detail="没有有效的交易对")
        
        success = db_manager.save_selected_symbols(valid_symbols)
        
        if not success:
            raise HTTPException(status_code=500, detail="保存交易对失败")
        
        logger.info(f"已保存 {len(valid_symbols)} 个交易对")
        
        return JSONResponse(content={
            "success": True,
            "message": "交易对保存成功",
            "data": {
                "symbols": valid_symbols,
                "count": len(valid_symbols)
            }
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"保存交易对失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/symbols/available")
async def get_available_symbols():
    """获取交易所可用的交易对"""
    try:
        # 从当前配置获取交易所名称，如果没有配置则使用默认
        try:
            config = db_manager.load_exchange_config()
            exchange = config.get("exchange_name", "okx") if config else "okx"
        except Exception:
            exchange = "okx"

        # 这里应该从交易所客户端获取实际的交易对列表
        # 目前返回模拟数据
        mock_symbols = [
            "BTC/USDT:USDT",
            "ETH/USDT:USDT",
            "BNB/USDT:USDT",
            "ADA/USDT:USDT",
            "SOL/USDT:USDT",
            "DOGE/USDT:USDT",
            "MATIC/USDT:USDT",
            "DOT/USDT:USDT",
            "AVAX/USDT:USDT",
            "LINK/USDT:USDT"
        ]

        return JSONResponse(content={
            "success": True,
            "data": {
                "exchange": exchange,
                "symbols": mock_symbols,
                "count": len(mock_symbols)
            }
        })

    except Exception as e:
        logger.error(f"获取可用交易对失败: {e}")
        return JSONResponse(content={"success": False, "message": str(e)}, status_code=500)


@router.post("/exchange/test")
async def test_exchange_connection():
    """测试交易所连接"""
    try:
        # 获取当前配置
        config = db_manager.load_exchange_config()

        if not config:
            return JSONResponse(content={"success": False, "message": "请先配置交易所信息"}, status_code=400)

        # 验证必要字段
        if not config.api_key or not config.secret_key:
            return JSONResponse(content={"success": False, "message": "请填写完整的API Key和Secret Key"}, status_code=400)

        # 对于OKX，验证Passphrase
        if config.exchange_name.lower() == 'okx' and not config.passphrase:
            return JSONResponse(content={"success": False, "message": "OKX交易所需要填写Passphrase"}, status_code=400)

        # 实际测试连接
        balance_info = await test_exchange_api_connection(config)

        return JSONResponse(content={
            "success": True,
            "message": "连接测试成功",
            "data": {
                "exchange": config.exchange_name,
                "sandbox_mode": config.sandbox_mode,
                "balance_info": balance_info
            }
        })

    except Exception as e:
        logger.error(f"测试连接失败: {e}")
        return JSONResponse(content={"success": False, "message": f"连接测试失败: {str(e)}"}, status_code=500)


@router.get("/validation-rules")
async def get_validation_rules():
    """获取配置验证规则"""
    try:
        rules = {
            "exchange": {
                "supported_exchanges": ["okx", "binance", "huobi", "bybit"],
                "api_key_min_length": 8,
                "secret_key_min_length": 8
            },
            "trading_parameters": {
                "max_leverage": {"min": 1, "max": 100},
                "max_position_ratio": {"min": 0.1, "max": 1.0},
                "confidence_threshold": {"min": 0, "max": 100},
                "stop_loss_percentage": {"min": 0.01, "max": 0.5},
                "take_profit_percentage": {"min": 0.01, "max": 1.0}
            },
            "risk_parameters": {
                "max_leverage": {"min": 1, "max": 100},
                "max_position_ratio": {"min": 0.1, "max": 1.0},
                "min_balance_threshold": {"min": 10, "max": 10000},
                "confidence_threshold": {"min": 0, "max": 100}
            }
        }
        
        return JSONResponse(content=rules)

    except Exception as e:
        logger.error(f"获取验证规则失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def test_exchange_api_connection(config: ExchangeConfig) -> dict:
    """
    实际测试交易所API连接

    Args:
        config: 交易所配置

    Returns:
        dict: 账户余额信息

    Raises:
        Exception: 连接失败时抛出异常
    """
    try:
        # 根据交易所名称创建CCXT实例
        exchange_class = getattr(ccxt, config.exchange_name.lower())

        # 构建API配置
        api_config = {
            'apiKey': config.api_key,
            'secret': config.secret_key,
            'sandbox': config.sandbox_mode,
            'enableRateLimit': True,
        }

        # 添加交易所特有配置
        if config.exchange_name.lower() == 'okx':
            # OKX在CCXT中需要password字段（对应OKX的passphrase）
            if config.passphrase:
                api_config['password'] = config.passphrase

        # 创建交易所实例
        exchange = exchange_class(api_config)

        # 测试连接 - 获取账户余额
        logger.info(f"正在测试 {config.exchange_name} 连接...")

        # 使用asyncio运行同步的CCXT方法
        loop = asyncio.get_event_loop()
        balance = await loop.run_in_executor(None, exchange.fetch_balance)

        # 提取有用的余额信息
        balance_info = {
            "total_balance": 0,
            "available_balance": 0,
            "currencies": []
        }

        if balance and 'total' in balance:
            # 计算总余额（USDT等值）
            for currency, amount in balance['total'].items():
                if amount and amount > 0:
                    balance_info["currencies"].append({
                        "currency": currency,
                        "total": amount,
                        "free": balance.get('free', {}).get(currency, 0),
                        "used": balance.get('used', {}).get(currency, 0)
                    })

                    # 简单计算总余额（这里可以后续优化为实际汇率转换）
                    if currency in ['USDT', 'USD', 'USDC']:
                        balance_info["total_balance"] += amount
                        balance_info["available_balance"] += balance.get('free', {}).get(currency, 0)

        logger.info(f"{config.exchange_name} 连接测试成功，账户余额: {balance_info['total_balance']:.2f} USDT")

        return balance_info

    except Exception as e:
        logger.error(f"{config.exchange_name} 连接测试失败: {e}")
        raise Exception(f"无法连接到 {config.exchange_name}: {str(e)}")

    finally:
        # 关闭交易所连接
        if 'exchange' in locals():
            try:
                exchange.close()
            except:
                pass


@router.get("/ai")
async def get_ai_settings():
    """获取AI引擎设置"""
    try:
        config = db_manager.load_ai_config()

        if not config:
            # 返回默认设置
            default_settings = {
                "deepseek_api_key": "",
                "analysis_interval": 300,
                "lookback_period": 24,
                "enable_opening_engine": True,
                "enable_position_engine": True,
                "configured": False
            }
            return JSONResponse(content={"success": True, "data": default_settings})

        # 隐藏敏感信息
        safe_config = {
            "deepseek_api_key": config.deepseek_api_key[:8] + "..." if config.deepseek_api_key else "",
            "analysis_interval": config.analysis_interval,
            "lookback_period": config.lookback_period,
            "enable_opening_engine": config.enable_opening_engine,
            "enable_position_engine": config.enable_position_engine,
            "configured": True
        }

        return JSONResponse(content={"success": True, "data": safe_config})

    except Exception as e:
        logger.error(f"获取AI设置失败: {e}")
        return JSONResponse(content={"success": False, "message": str(e)}, status_code=500)


@router.post("/ai")
async def save_ai_settings(settings: AISettingsRequest):
    """保存AI引擎设置"""
    try:
        # 验证DeepSeek API密钥格式
        if not settings.deepseek_api_key.startswith('sk-'):
            raise HTTPException(status_code=400, detail="DeepSeek API密钥格式不正确，应以'sk-'开头")

        # 创建AI配置对象
        ai_config = AIConfig(
            deepseek_api_key=settings.deepseek_api_key,
            analysis_interval=settings.analysis_interval,
            lookback_period=settings.lookback_period,
            enable_opening_engine=settings.enable_opening_engine,
            enable_position_engine=settings.enable_position_engine
        )

        success = db_manager.save_ai_config(ai_config)

        if not success:
            raise HTTPException(status_code=500, detail="保存AI设置失败")

        logger.info("AI设置已保存到数据库")

        return JSONResponse(content={
            "success": True,
            "message": "AI设置保存成功",
            "data": {
                "analysis_interval": settings.analysis_interval,
                "lookback_period": settings.lookback_period,
                "enable_opening_engine": settings.enable_opening_engine,
                "enable_position_engine": settings.enable_position_engine
            }
        })

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"保存AI设置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/ai/test")
async def test_ai_connection():
    """测试AI连接"""
    try:
        # 这里应该实际测试DeepSeek API连接
        # 目前返回模拟结果

        return JSONResponse(content={
            "success": True,
            "message": "AI连接测试成功",
            "data": {
                "api_status": "connected",
                "response_time": "120ms"
            }
        })

    except Exception as e:
        logger.error(f"AI连接测试失败: {e}")
        return JSONResponse(content={"success": False, "message": str(e)}, status_code=500)
