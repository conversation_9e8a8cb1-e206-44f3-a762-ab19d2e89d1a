# =============================================================================
# DeepSeek量化交易系统 - 环境配置示例文件
# =============================================================================
# 使用说明：
# 1. 复制此文件为 .env
# 2. 填写实际的配置值
# 3. 确保 .env 文件已添加到 .gitignore 中
# 4. 敏感信息（如API密钥）建议通过Web界面配置
# =============================================================================

# =============================================================================
# 环境配置
# =============================================================================
# 运行环境：development（开发）、production（生产）
ENVIRONMENT=development

# 调试模式（统一控制所有调试功能）
DEBUG_MODE=true

# 热重载功能（仅在开发环境+调试模式下生效）
HOT_RELOAD=true

# =============================================================================
# DeepSeek AI API配置
# =============================================================================
# 注意：DeepSeek API密钥建议通过Web界面的"AI设置"页面配置
# 这里只配置API服务的基础信息
DEEPSEEK_API_BASE=https://api.deepseek.com/v1
DEEPSEEK_MODEL=deepseek-chat

# 可选：在此处配置API密钥（不推荐，建议通过Web界面配置）
# DEEPSEEK_API_KEY=your-deepseek-api-key

# =============================================================================
# 数据库配置
# =============================================================================
# SQLite数据库文件路径
DATABASE_PATH=data/trading_system.db

# 注意：数据库加密功能已移除，数据以明文存储
# 敏感信息（如API密钥）建议通过Web界面配置，系统会安全存储

# =============================================================================
# Web服务配置
# =============================================================================
# Web服务器主机地址
WEB_HOST=127.0.0.1

# Web服务器端口
WEB_PORT=8000

# 注意：WEB_DEBUG已废弃，现在使用DEBUG_MODE统一控制调试模式

# Web会话密钥（用于会话加密，可选）
WEB_SECRET_KEY=your-web-secret-key-for-sessions

# =============================================================================
# 日志配置
# =============================================================================
# 日志级别：DEBUG、INFO、WARNING、ERROR、CRITICAL
LOG_LEVEL=INFO

# 日志文件路径
LOG_FILE_PATH=logs/system.log

# =============================================================================
# CORS配置
# =============================================================================
# 允许的前端域名（注意：端口应与WEB_PORT保持一致）
CORS_ORIGINS=["http://localhost:3000","http://127.0.0.1:3000","http://localhost:8000","http://127.0.0.1:8000"]

# 是否允许携带凭证
CORS_ALLOW_CREDENTIALS=true

# =============================================================================
# 可选的高级配置
# =============================================================================

# 交易系统默认配置
DEFAULT_MAX_LEVERAGE=10
DEFAULT_MAX_POSITION_RATIO=0.5
DEFAULT_OPENING_CONFIDENCE_THRESHOLD=70
DEFAULT_POSITION_CONFIDENCE_THRESHOLD=60
DEFAULT_STOP_LOSS_PERCENTAGE=0.05
DEFAULT_TAKE_PROFIT_PERCENTAGE=0.1

# AI分析配置
AI_ANALYSIS_INTERVAL=300
AI_LOOKBACK_PERIOD=24
AI_ENABLE_OPENING_ENGINE=true
AI_ENABLE_POSITION_ENGINE=true

# 风险管理配置
RISK_MAX_LEVERAGE=20
RISK_MAX_EXPOSURE_RATIO=0.8
RISK_MIN_BALANCE_THRESHOLD=100
RISK_MAX_DRAWDOWN=0.2

# 系统监控配置
MONITORING_ENABLED=true
MONITORING_INTERVAL=60
MONITORING_ALERT_THRESHOLD=0.1

# 通知配置（可选）
NOTIFICATION_ENABLED=false
NOTIFICATION_EMAIL=
NOTIFICATION_WEBHOOK_URL=
