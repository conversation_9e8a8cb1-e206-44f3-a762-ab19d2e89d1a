#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek量化交易系统交易所客户端模块

此模块封装CCXT功能，提供统一的交易所访问接口，包括：
1. 交易所连接和认证
2. 市场数据获取
3. 账户信息查询
4. 交易操作执行
5. 错误处理和重试机制
"""

import asyncio
import time
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import ccxt
import ccxt.async_support as ccxt_async

from src.data.models import ExchangeConfig, OHLCVData, Position, Order, AccountBalance, TradingSide, OrderType, OrderStatus
from src.utils.exceptions import (
    ExchangeConnectionError, ExchangeAPIError, ExchangeAuthenticationError,
    InsufficientBalanceError, OrderExecutionError
)
from src.utils.logger import get_logger, log_api_call, log_trading_action
from src.utils.validators import TradingValidator, ValidationResult

logger = get_logger(__name__)


class ExchangeClient:
    """交易所客户端类
    
    封装CCXT功能，提供统一的交易所访问接口。
    """
    
    def __init__(self, config: ExchangeConfig):
        """初始化交易所客户端
        
        Args:
            config: 交易所配置
        """
        self.config = config
        self.exchange = None
        self.async_exchange = None
        self.is_connected = False
        self.last_request_time = 0
        self.rate_limit_delay = 0.1  # 默认请求间隔100ms
        
        # 支持的交易所映射
        self.supported_exchanges = {
            'okx': ccxt.okx,
            'binance': ccxt.binance,
            'huobi': ccxt.huobi,
            'bybit': ccxt.bybit,
            'bitget': ccxt.bitget,
            'gate': ccxt.gateio,
            'kucoin': ccxt.kucoin
        }
        
        self.async_supported_exchanges = {
            'okx': ccxt_async.okx,
            'binance': ccxt_async.binance,
            'huobi': ccxt_async.huobi,
            'bybit': ccxt_async.bybit,
            'bitget': ccxt_async.bitget,
            'gate': ccxt_async.gateio,
            'kucoin': ccxt_async.kucoin
        }
    
    def _rate_limit(self):
        """实现请求频率限制"""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time
        
        if time_since_last_request < self.rate_limit_delay:
            sleep_time = self.rate_limit_delay - time_since_last_request
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def connect(self) -> bool:
        """连接到交易所
        
        Returns:
            bool: 连接是否成功
        """
        try:
            exchange_name = self.config.exchange_name.lower()
            
            if exchange_name not in self.supported_exchanges:
                raise ExchangeConnectionError(
                    exchange_name,
                    f"不支持的交易所: {exchange_name}"
                )
            
            # 创建交易所实例
            exchange_class = self.supported_exchanges[exchange_name]
            async_exchange_class = self.async_supported_exchanges[exchange_name]
            
            # 配置API参数
            api_config = {
                'apiKey': self.config.api_key,
                'secret': self.config.secret_key,
                'sandbox': self.config.sandbox_mode,
                'enableRateLimit': True,
                'timeout': 30000,  # 30秒超时
            }
            
            # OKX在CCXT中需要password字段（对应OKX的passphrase）
            if exchange_name == 'okx' and self.config.passphrase:
                api_config['password'] = self.config.passphrase
            
            # 创建同步和异步实例
            self.exchange = exchange_class(api_config)
            self.async_exchange = async_exchange_class(api_config)
            
            # 测试连接
            try:
                markets = self.exchange.load_markets()
                logger.info(f"成功连接到交易所 {exchange_name}，加载了 {len(markets)} 个交易对")
                self.is_connected = True
                return True
                
            except ccxt.AuthenticationError as e:
                raise ExchangeAuthenticationError(exchange_name, original_exception=e)
            except ccxt.NetworkError as e:
                raise ExchangeConnectionError(exchange_name, f"网络连接失败: {e}", original_exception=e)
            except Exception as e:
                raise ExchangeAPIError(exchange_name, str(e), original_exception=e)
                
        except Exception as e:
            logger.error(f"连接交易所失败: {e}")
            self.is_connected = False
            raise
    
    def disconnect(self) -> None:
        """断开交易所连接"""
        try:
            if self.async_exchange:
                # 异步关闭需要在事件循环中执行
                try:
                    # 尝试获取当前事件循环
                    try:
                        loop = asyncio.get_running_loop()
                        # 如果事件循环正在运行，创建任务
                        asyncio.create_task(self.async_exchange.close())
                    except RuntimeError:
                        # 没有运行中的事件循环，创建新的
                        asyncio.run(self.async_exchange.close())
                except Exception as e:
                    logger.warning(f"关闭异步交易所连接失败: {e}")

            self.exchange = None
            self.async_exchange = None
            self.is_connected = False
            logger.info("交易所连接已断开")

        except Exception as e:
            logger.error(f"断开交易所连接时发生错误: {e}")
    
    def _ensure_connected(self):
        """确保已连接到交易所"""
        if not self.is_connected or not self.exchange:
            raise ExchangeConnectionError(
                self.config.exchange_name,
                "未连接到交易所，请先调用connect()方法"
            )
    
    def _handle_ccxt_exception(self, e: Exception, operation: str) -> None:
        """处理CCXT异常
        
        Args:
            e: CCXT异常
            operation: 操作名称
        """
        if isinstance(e, ccxt.AuthenticationError):
            raise ExchangeAuthenticationError(self.config.exchange_name, original_exception=e)
        elif isinstance(e, ccxt.InsufficientFunds):
            raise InsufficientBalanceError(0, 0, original_exception=e)
        elif isinstance(e, ccxt.NetworkError):
            raise ExchangeConnectionError(
                self.config.exchange_name,
                f"网络错误: {operation}",
                original_exception=e
            )
        elif isinstance(e, ccxt.ExchangeError):
            raise ExchangeAPIError(
                self.config.exchange_name,
                f"{operation} 失败: {str(e)}",
                original_exception=e
            )
        else:
            raise ExchangeAPIError(
                self.config.exchange_name,
                f"{operation} 发生未知错误: {str(e)}",
                original_exception=e
            )
    
    @log_api_call("Exchange", "fetch_ohlcv")
    async def fetch_ohlcv(self, symbol: str, timeframe: str, limit: int = 200) -> List[OHLCVData]:
        """获取OHLCV数据
        
        Args:
            symbol: 交易对符号
            timeframe: 时间周期
            limit: 数据数量限制
        
        Returns:
            List[OHLCVData]: OHLCV数据列表
        """
        try:
            self._ensure_connected()
            
            # 验证参数
            symbol_result = TradingValidator.validate_symbol(symbol)
            if not symbol_result.is_valid:
                raise ValueError(f"无效的交易对符号: {symbol_result.errors}")
            
            # 获取数据
            ohlcv_data = await self.async_exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            
            # 转换为数据模型
            result = []
            for data in ohlcv_data:
                ohlcv = OHLCVData(
                    timestamp=int(data[0]),
                    open=float(data[1]),
                    high=float(data[2]),
                    low=float(data[3]),
                    close=float(data[4]),
                    volume=float(data[5])
                )
                result.append(ohlcv)
            
            logger.info(f"获取 {symbol} {timeframe} K线数据成功: {len(result)} 条")
            return result
            
        except Exception as e:
            self._handle_ccxt_exception(e, f"获取K线数据 {symbol} {timeframe}")
    
    def fetch_ohlcv_sync(self, symbol: str, timeframe: str, limit: int = 200) -> List[OHLCVData]:
        """同步获取OHLCV数据
        
        Args:
            symbol: 交易对符号
            timeframe: 时间周期
            limit: 数据数量限制
        
        Returns:
            List[OHLCVData]: OHLCV数据列表
        """
        try:
            self._ensure_connected()
            self._rate_limit()
            
            # 验证参数
            symbol_result = TradingValidator.validate_symbol(symbol)
            if not symbol_result.is_valid:
                raise ValueError(f"无效的交易对符号: {symbol_result.errors}")
            
            # 获取数据
            ohlcv_data = self.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            
            # 转换为数据模型
            result = []
            for data in ohlcv_data:
                ohlcv = OHLCVData(
                    timestamp=int(data[0]),
                    open=float(data[1]),
                    high=float(data[2]),
                    low=float(data[3]),
                    close=float(data[4]),
                    volume=float(data[5])
                )
                result.append(ohlcv)
            
            logger.info(f"获取 {symbol} {timeframe} K线数据成功: {len(result)} 条")
            return result
            
        except Exception as e:
            self._handle_ccxt_exception(e, f"获取K线数据 {symbol} {timeframe}")
    
    def fetch_markets(self) -> List[Dict[str, Any]]:
        """获取所有交易对信息
        
        Returns:
            List[Dict[str, Any]]: 交易对信息列表
        """
        try:
            self._ensure_connected()
            self._rate_limit()
            
            markets = self.exchange.fetch_markets()
            
            # 过滤永续合约
            futures_markets = []
            for market in markets:
                if market.get('type') == 'swap' or market.get('contract', False):
                    futures_markets.append(market)
            
            logger.info(f"获取交易对信息成功: {len(futures_markets)} 个永续合约")
            return futures_markets
            
        except Exception as e:
            self._handle_ccxt_exception(e, "获取交易对信息")
    
    def fetch_market_info(self, symbol: str) -> Dict[str, Any]:
        """获取特定交易对信息
        
        Args:
            symbol: 交易对符号
        
        Returns:
            Dict[str, Any]: 交易对信息
        """
        try:
            self._ensure_connected()
            
            markets = self.exchange.markets
            if symbol not in markets:
                raise ValueError(f"交易对 {symbol} 不存在")
            
            market_info = markets[symbol]
            logger.info(f"获取交易对 {symbol} 信息成功")
            return market_info
            
        except Exception as e:
            self._handle_ccxt_exception(e, f"获取交易对信息 {symbol}")
    
    def __enter__(self):
        """上下文管理器入口"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect()

    def fetch_balance(self) -> Dict[str, AccountBalance]:
        """获取账户余额

        Returns:
            Dict[str, AccountBalance]: 账户余额信息
        """
        try:
            self._ensure_connected()
            self._rate_limit()

            balance_data = self.exchange.fetch_balance()

            # 转换为数据模型
            balances = {}
            for currency, balance_info in balance_data.items():
                if currency not in ['info', 'free', 'used', 'total'] and isinstance(balance_info, dict):
                    balance = AccountBalance(
                        currency=currency,
                        total=float(balance_info.get('total', 0)),
                        available=float(balance_info.get('free', 0)),
                        used=float(balance_info.get('used', 0))
                    )
                    balances[currency] = balance

            logger.info(f"获取账户余额成功: {len(balances)} 个币种")
            return balances

        except Exception as e:
            self._handle_ccxt_exception(e, "获取账户余额")

    def fetch_positions(self) -> List[Position]:
        """获取持仓信息

        Returns:
            List[Position]: 持仓信息列表
        """
        try:
            self._ensure_connected()
            self._rate_limit()

            positions_data = self.exchange.fetch_positions()

            # 转换为数据模型
            positions = []
            for pos_data in positions_data:
                if float(pos_data.get('contracts', 0)) > 0:  # 只返回有持仓的
                    # 转换交易方向字符串为枚举
                    side_str = pos_data['side'].lower()
                    if side_str in ['long', 'buy']:
                        side = TradingSide.LONG
                    elif side_str in ['short', 'sell']:
                        side = TradingSide.SHORT
                    else:
                        side = TradingSide.LONG  # 默认值

                    # 安全地转换数值，避免NaN
                    def safe_float(value, default=0.0):
                        """安全地转换为浮点数，避免NaN"""
                        if value is None or value == '':
                            return default
                        try:
                            return float(value)
                        except (ValueError, TypeError):
                            return default

                    def safe_int(value, default=1):
                        """安全地转换为整数"""
                        if value is None or value == '':
                            return default
                        try:
                            return int(value)
                        except (ValueError, TypeError):
                            return default

                    position = Position(
                        symbol=pos_data['symbol'],
                        side=side,
                        amount=safe_float(pos_data.get('contracts', 0)),
                        entry_price=safe_float(pos_data.get('entryPrice')),
                        current_price=safe_float(pos_data.get('markPrice')),
                        leverage=safe_int(pos_data.get('leverage', 1)),
                        unrealized_pnl=safe_float(pos_data.get('unrealizedPnl')),
                        unrealized_pnl_percentage=safe_float(pos_data.get('percentage')),
                        margin_used=safe_float(pos_data.get('initialMargin'))
                    )
                    positions.append(position)

            logger.info(f"获取持仓信息成功: {len(positions)} 个持仓")
            return positions

        except Exception as e:
            self._handle_ccxt_exception(e, "获取持仓信息")

    def fetch_orders(self, symbol: Optional[str] = None) -> List[Order]:
        """获取订单信息

        Args:
            symbol: 交易对符号，如果为None则获取所有订单

        Returns:
            List[Order]: 订单信息列表
        """
        try:
            self._ensure_connected()
            self._rate_limit()

            if symbol:
                orders_data = self.exchange.fetch_open_orders(symbol)
            else:
                orders_data = self.exchange.fetch_open_orders()

            # 转换为数据模型
            orders = []
            for order_data in orders_data:
                # 转换交易方向字符串为枚举
                side_str = order_data['side'].lower()
                if side_str in ['long', 'buy']:
                    side = TradingSide.BUY
                elif side_str in ['short', 'sell']:
                    side = TradingSide.SELL
                else:
                    side = TradingSide.BUY  # 默认值

                # 转换订单类型字符串为枚举
                order_type_str = order_data['type'].lower()
                if order_type_str == 'market':
                    order_type = OrderType.MARKET
                elif order_type_str == 'limit':
                    order_type = OrderType.LIMIT
                elif order_type_str == 'stop':
                    order_type = OrderType.STOP
                elif order_type_str == 'stop_limit':
                    order_type = OrderType.STOP_LIMIT
                else:
                    order_type = OrderType.MARKET  # 默认值

                # 转换订单状态字符串为枚举
                status_str = order_data['status'].lower()
                if status_str == 'open':
                    status = OrderStatus.OPEN
                elif status_str == 'closed':
                    status = OrderStatus.CLOSED
                elif status_str == 'canceled':
                    status = OrderStatus.CANCELED
                elif status_str == 'rejected':
                    status = OrderStatus.REJECTED
                else:
                    status = OrderStatus.PENDING  # 默认值

                order = Order(
                    id=order_data['id'],
                    symbol=order_data['symbol'],
                    side=side,
                    amount=float(order_data['amount']),
                    price=float(order_data['price']) if order_data['price'] else None,
                    order_type=order_type,
                    status=status,
                    filled_amount=float(order_data.get('filled', 0)),
                    average_price=float(order_data['average']) if order_data['average'] else None,
                    created_at=int(order_data['timestamp']) if order_data['timestamp'] else None
                )
                orders.append(order)

            logger.info(f"获取订单信息成功: {len(orders)} 个订单")
            return orders

        except Exception as e:
            self._handle_ccxt_exception(e, f"获取订单信息 {symbol or 'all'}")

    @log_trading_action("市场价开仓")
    async def create_market_order(self, symbol: str, side: str, amount: float, leverage: int = 1) -> Dict[str, Any]:
        """创建市场价订单

        Args:
            symbol: 交易对符号
            side: 交易方向 (buy/sell)
            amount: 交易数量
            leverage: 杠杆倍数

        Returns:
            Dict[str, Any]: 订单信息
        """
        try:
            self._ensure_connected()

            # 验证参数
            symbol_result = TradingValidator.validate_symbol(symbol)
            side_result = TradingValidator.validate_side(side)
            amount_result = TradingValidator.validate_amount(amount)
            leverage_result = TradingValidator.validate_leverage(leverage)

            validation_errors = []
            for result in [symbol_result, side_result, amount_result, leverage_result]:
                if not result.is_valid:
                    validation_errors.extend(result.errors)

            if validation_errors:
                raise ValueError(f"参数验证失败: {validation_errors}")

            # 设置杠杆（如果支持）
            try:
                await self.async_exchange.set_leverage(leverage, symbol)
                logger.info(f"设置杠杆成功: {symbol} {leverage}x")
            except Exception as e:
                logger.warning(f"设置杠杆失败: {e}")

            # 创建订单
            order = await self.async_exchange.create_market_order(symbol, side, amount)

            logger.info(f"市场价订单创建成功: {symbol} {side} {amount}")
            return order

        except Exception as e:
            self._handle_ccxt_exception(e, f"创建市场价订单 {symbol} {side} {amount}")

    @log_trading_action("限价开仓")
    async def create_limit_order(self, symbol: str, side: str, amount: float, price: float, leverage: int = 1) -> Dict[str, Any]:
        """创建限价订单

        Args:
            symbol: 交易对符号
            side: 交易方向 (buy/sell)
            amount: 交易数量
            price: 限价价格
            leverage: 杠杆倍数

        Returns:
            Dict[str, Any]: 订单信息
        """
        try:
            self._ensure_connected()

            # 验证参数
            symbol_result = TradingValidator.validate_symbol(symbol)
            side_result = TradingValidator.validate_side(side)
            amount_result = TradingValidator.validate_amount(amount)
            price_result = TradingValidator.validate_price(price)
            leverage_result = TradingValidator.validate_leverage(leverage)

            validation_errors = []
            for result in [symbol_result, side_result, amount_result, price_result, leverage_result]:
                if not result.is_valid:
                    validation_errors.extend(result.errors)

            if validation_errors:
                raise ValueError(f"参数验证失败: {validation_errors}")

            # 设置杠杆（如果支持）
            try:
                await self.async_exchange.set_leverage(leverage, symbol)
                logger.info(f"设置杠杆成功: {symbol} {leverage}x")
            except Exception as e:
                logger.warning(f"设置杠杆失败: {e}")

            # 创建订单
            order = await self.async_exchange.create_limit_order(symbol, side, amount, price)

            logger.info(f"限价订单创建成功: {symbol} {side} {amount} @ {price}")
            return order

        except Exception as e:
            self._handle_ccxt_exception(e, f"创建限价订单 {symbol} {side} {amount} @ {price}")

    @log_trading_action("取消订单")
    async def cancel_order(self, order_id: str, symbol: str) -> Dict[str, Any]:
        """取消订单

        Args:
            order_id: 订单ID
            symbol: 交易对符号

        Returns:
            Dict[str, Any]: 取消结果
        """
        try:
            self._ensure_connected()

            result = await self.async_exchange.cancel_order(order_id, symbol)

            logger.info(f"订单取消成功: {order_id}")
            return result

        except Exception as e:
            self._handle_ccxt_exception(e, f"取消订单 {order_id}")

    @log_trading_action("平仓")
    async def close_position(self, symbol: str, side: str, amount: Optional[float] = None) -> Dict[str, Any]:
        """平仓

        Args:
            symbol: 交易对符号
            side: 持仓方向
            amount: 平仓数量，如果为None则全部平仓

        Returns:
            Dict[str, Any]: 平仓结果
        """
        try:
            self._ensure_connected()

            # 获取当前持仓
            positions = self.fetch_positions()
            target_position = None

            for position in positions:
                if position.symbol == symbol and position.side == side:
                    target_position = position
                    break

            if not target_position:
                raise ValueError(f"未找到持仓: {symbol} {side}")

            # 确定平仓数量
            close_amount = amount if amount else target_position.amount

            # 确定平仓方向（与持仓方向相反）
            close_side = 'sell' if side in ['long', 'buy'] else 'buy'

            # 执行平仓
            order = await self.create_market_order(symbol, close_side, close_amount)

            logger.info(f"平仓成功: {symbol} {side} {close_amount}")
            return order

        except Exception as e:
            self._handle_ccxt_exception(e, f"平仓 {symbol} {side}")

    def ensure_single_direction(self, symbol: str, new_side: str) -> bool:
        """确保单向持仓（如果有相反方向的持仓，先平仓）

        Args:
            symbol: 交易对符号
            new_side: 新的持仓方向

        Returns:
            bool: 是否需要等待平仓完成
        """
        try:
            positions = self.fetch_positions()

            for position in positions:
                if position.symbol == symbol:
                    # 检查是否有相反方向的持仓
                    if ((new_side in ['long', 'buy'] and position.side in ['short', 'sell']) or
                        (new_side in ['short', 'sell'] and position.side in ['long', 'buy'])):

                        logger.info(f"检测到相反方向持仓，先平仓: {symbol} {position.side}")

                        # 异步平仓
                        asyncio.create_task(self.close_position(symbol, position.side))
                        return True

            return False

        except Exception as e:
            logger.error(f"检查单向持仓失败: {e}")
            return False

    @log_trading_action("设置止盈止损")
    async def set_stop_loss_take_profit(self, symbol: str, position_side: str,
                                       stop_loss_price: Optional[float] = None,
                                       take_profit_price: Optional[float] = None) -> Dict[str, Any]:
        """设置止盈止损订单

        Args:
            symbol: 交易对符号
            position_side: 持仓方向 (long/short)
            stop_loss_price: 止损价格
            take_profit_price: 止盈价格

        Returns:
            Dict[str, Any]: 设置结果
        """
        try:
            self._ensure_connected()

            # 获取当前持仓
            positions = self.fetch_positions()
            target_position = None

            for position in positions:
                if position.symbol == symbol and position.side.value.lower() in [position_side.lower()]:
                    target_position = position
                    break

            if not target_position:
                raise ValueError(f"未找到持仓: {symbol} {position_side}")

            results = {}

            # 设置止损订单
            if stop_loss_price:
                stop_side = 'sell' if position_side.lower() in ['long', 'buy'] else 'buy'

                try:
                    stop_order = await self.async_exchange.create_order(
                        symbol=symbol,
                        type='stop_market',
                        side=stop_side,
                        amount=target_position.amount,
                        params={'stopPrice': stop_loss_price}
                    )
                    results['stop_loss'] = stop_order
                    logger.info(f"止损订单设置成功: {symbol} @ {stop_loss_price}")
                except Exception as e:
                    logger.error(f"设置止损订单失败: {e}")
                    results['stop_loss_error'] = str(e)

            # 设置止盈订单
            if take_profit_price:
                profit_side = 'sell' if position_side.lower() in ['long', 'buy'] else 'buy'

                try:
                    profit_order = await self.create_limit_order(
                        symbol=symbol,
                        side=profit_side,
                        amount=target_position.amount,
                        price=take_profit_price
                    )
                    results['take_profit'] = profit_order
                    logger.info(f"止盈订单设置成功: {symbol} @ {take_profit_price}")
                except Exception as e:
                    logger.error(f"设置止盈订单失败: {e}")
                    results['take_profit_error'] = str(e)

            return results

        except Exception as e:
            self._handle_ccxt_exception(e, f"设置止盈止损 {symbol}")

    @log_trading_action("修改订单")
    async def modify_order(self, order_id: str, symbol: str,
                          new_amount: Optional[float] = None,
                          new_price: Optional[float] = None) -> Dict[str, Any]:
        """修改订单

        Args:
            order_id: 订单ID
            symbol: 交易对符号
            new_amount: 新的数量
            new_price: 新的价格

        Returns:
            Dict[str, Any]: 修改结果
        """
        try:
            self._ensure_connected()
            self._rate_limit()

            # 构建修改参数
            params = {}
            if new_amount is not None:
                params['amount'] = new_amount
            if new_price is not None:
                params['price'] = new_price

            # 修改订单
            result = await self.async_exchange.edit_order(order_id, symbol, **params)

            logger.info(f"订单修改成功: {order_id}")
            return result

        except Exception as e:
            self._handle_ccxt_exception(e, f"修改订单 {order_id}")

    def get_position_by_symbol(self, symbol: str) -> Optional[Position]:
        """根据交易对获取持仓信息

        Args:
            symbol: 交易对符号

        Returns:
            Optional[Position]: 持仓信息，如果没有持仓则返回None
        """
        try:
            positions = self.fetch_positions()

            for position in positions:
                if position.symbol == symbol:
                    return position

            return None

        except Exception as e:
            logger.error(f"获取持仓信息失败: {e}")
            return None

    def calculate_position_size(self, symbol: str, risk_amount: float,
                               entry_price: float, stop_loss_price: float,
                               leverage: int = 1) -> float:
        """计算仓位大小

        Args:
            symbol: 交易对符号
            risk_amount: 风险金额
            entry_price: 入场价格
            stop_loss_price: 止损价格
            leverage: 杠杆倍数

        Returns:
            float: 建议的仓位大小
        """
        try:
            # 计算价格差异
            price_diff = abs(entry_price - stop_loss_price)

            # 计算风险百分比
            risk_percentage = price_diff / entry_price

            # 计算仓位大小
            position_size = risk_amount / (price_diff * leverage)

            logger.info(f"仓位计算: 风险金额={risk_amount}, 价格差异={price_diff}, 建议仓位={position_size}")
            return position_size

        except Exception as e:
            logger.error(f"计算仓位大小失败: {e}")
            return 0.0

    @log_trading_action("设置止盈止损")
    async def set_stop_loss_take_profit(self, symbol: str, position_side: str,
                                       stop_loss_price: Optional[float] = None,
                                       take_profit_price: Optional[float] = None) -> Dict[str, Any]:
        """设置止盈止损订单

        Args:
            symbol: 交易对符号
            position_side: 持仓方向 (long/short)
            stop_loss_price: 止损价格
            take_profit_price: 止盈价格

        Returns:
            Dict[str, Any]: 设置结果
        """
        try:
            self._ensure_connected()

            # 获取当前持仓
            positions = self.fetch_positions()
            target_position = None

            for position in positions:
                if position.symbol == symbol and position.side.value.lower() in [position_side.lower()]:
                    target_position = position
                    break

            if not target_position:
                raise ValueError(f"未找到持仓: {symbol} {position_side}")

            results = {}

            # 设置止损订单
            if stop_loss_price:
                stop_side = 'sell' if position_side.lower() in ['long', 'buy'] else 'buy'

                try:
                    stop_order = await self.async_exchange.create_order(
                        symbol=symbol,
                        type='stop_market',
                        side=stop_side,
                        amount=target_position.amount,
                        params={'stopPrice': stop_loss_price}
                    )
                    results['stop_loss'] = stop_order
                    logger.info(f"止损订单设置成功: {symbol} @ {stop_loss_price}")
                except Exception as e:
                    logger.error(f"设置止损订单失败: {e}")
                    results['stop_loss_error'] = str(e)

            # 设置止盈订单
            if take_profit_price:
                profit_side = 'sell' if position_side.lower() in ['long', 'buy'] else 'buy'

                try:
                    profit_order = await self.create_limit_order(
                        symbol=symbol,
                        side=profit_side,
                        amount=target_position.amount,
                        price=take_profit_price
                    )
                    results['take_profit'] = profit_order
                    logger.info(f"止盈订单设置成功: {symbol} @ {take_profit_price}")
                except Exception as e:
                    logger.error(f"设置止盈订单失败: {e}")
                    results['take_profit_error'] = str(e)

            return results

        except Exception as e:
            self._handle_ccxt_exception(e, f"设置止盈止损 {symbol}")

    @log_trading_action("修改订单")
    async def modify_order(self, order_id: str, symbol: str,
                          new_amount: Optional[float] = None,
                          new_price: Optional[float] = None) -> Dict[str, Any]:
        """修改订单

        Args:
            order_id: 订单ID
            symbol: 交易对符号
            new_amount: 新的数量
            new_price: 新的价格

        Returns:
            Dict[str, Any]: 修改结果
        """
        try:
            self._ensure_connected()
            self._rate_limit()

            # 构建修改参数
            params = {}
            if new_amount is not None:
                params['amount'] = new_amount
            if new_price is not None:
                params['price'] = new_price

            # 修改订单
            result = await self.async_exchange.edit_order(order_id, symbol, **params)

            logger.info(f"订单修改成功: {order_id}")
            return result

        except Exception as e:
            self._handle_ccxt_exception(e, f"修改订单 {order_id}")

    def get_position_by_symbol(self, symbol: str) -> Optional[Position]:
        """根据交易对获取持仓信息

        Args:
            symbol: 交易对符号

        Returns:
            Optional[Position]: 持仓信息，如果没有持仓则返回None
        """
        try:
            positions = self.fetch_positions()

            for position in positions:
                if position.symbol == symbol:
                    return position

            return None

        except Exception as e:
            logger.error(f"获取持仓信息失败: {e}")
            return None

    def calculate_position_size(self, symbol: str, risk_amount: float,
                               entry_price: float, stop_loss_price: float,
                               leverage: int = 1) -> float:
        """计算仓位大小

        Args:
            symbol: 交易对符号
            risk_amount: 风险金额
            entry_price: 入场价格
            stop_loss_price: 止损价格
            leverage: 杠杆倍数

        Returns:
            float: 建议的仓位大小
        """
        try:
            # 计算价格差异
            price_diff = abs(entry_price - stop_loss_price)

            # 计算风险百分比
            risk_percentage = price_diff / entry_price

            # 计算仓位大小
            position_size = risk_amount / (price_diff * leverage)

            logger.info(f"仓位计算: 风险金额={risk_amount}, 价格差异={price_diff}, 建议仓位={position_size}")
            return position_size

        except Exception as e:
            logger.error(f"计算仓位大小失败: {e}")
            return 0.0
