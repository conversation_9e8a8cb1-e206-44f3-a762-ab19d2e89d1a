#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
持仓管理API逻辑测试

此测试验证持仓管理API的逻辑是否正确，包括数据转换和错误处理。
"""

import sys
import asyncio
from pathlib import Path
from unittest.mock import Mock, MagicMock
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.data.models import Position, TradingSide
from src.web.routes.trading import set_trading_coordinator, get_trading_coordinator
from src.utils.logger import get_logger

logger = get_logger(__name__)


def test_positions_api_logic():
    """测试持仓API逻辑"""
    
    print("\n=== 持仓API逻辑测试 ===")
    
    try:
        # 1. 创建模拟的持仓数据
        mock_positions = [
            Position(
                symbol="BTC/USDT:USDT",
                side=TradingSide.LONG,
                amount=0.1,
                entry_price=50000.0,
                current_price=52000.0,
                leverage=10,
                unrealized_pnl=200.0,
                unrealized_pnl_percentage=4.0,
                margin_used=500.0,
                created_at=int(datetime.now().timestamp() * 1000)
            ),
            Position(
                symbol="ETH/USDT:USDT",
                side=TradingSide.SHORT,
                amount=1.0,
                entry_price=3000.0,
                current_price=2950.0,
                leverage=5,
                unrealized_pnl=50.0,
                unrealized_pnl_percentage=1.67,
                margin_used=600.0,
                created_at=int(datetime.now().timestamp() * 1000)
            )
        ]
        
        # 2. 创建模拟的交易协调器
        mock_coordinator = Mock()
        mock_coordinator.current_positions = mock_positions
        
        # 3. 设置交易协调器引用
        set_trading_coordinator(mock_coordinator)
        
        # 4. 验证引用设置成功
        coordinator = get_trading_coordinator()
        assert coordinator is not None, "交易协调器引用设置失败"
        assert coordinator == mock_coordinator, "交易协调器引用不匹配"
        
        print("✅ 交易协调器引用设置成功")
        
        # 5. 测试持仓数据转换逻辑
        positions = getattr(coordinator, 'current_positions', [])
        assert len(positions) == 2, f"持仓数量不正确: {len(positions)}"
        
        # 验证第一个持仓
        pos1 = positions[0]
        assert pos1.symbol == "BTC/USDT:USDT"
        assert pos1.side == TradingSide.LONG
        assert pos1.amount == 0.1
        assert pos1.entry_price == 50000.0
        assert pos1.current_price == 52000.0
        assert pos1.leverage == 10
        assert pos1.unrealized_pnl == 200.0
        
        # 验证第二个持仓
        pos2 = positions[1]
        assert pos2.symbol == "ETH/USDT:USDT"
        assert pos2.side == TradingSide.SHORT
        assert pos2.amount == 1.0
        assert pos2.entry_price == 3000.0
        assert pos2.current_price == 2950.0
        assert pos2.leverage == 5
        assert pos2.unrealized_pnl == 50.0
        
        print("✅ 持仓数据验证成功")
        
        # 6. 计算统计数据
        total_unrealized_pnl = sum(p.unrealized_pnl for p in positions)
        total_margin_used = sum(p.margin_used for p in positions)
        avg_return_rate = sum(p.unrealized_pnl_percentage for p in positions) / len(positions)
        
        assert total_unrealized_pnl == 250.0, f"总盈亏计算错误: {total_unrealized_pnl}"
        assert total_margin_used == 1100.0, f"总保证金计算错误: {total_margin_used}"
        assert abs(avg_return_rate - 2.835) < 0.01, f"平均收益率计算错误: {avg_return_rate}"
        
        print("✅ 统计数据计算正确")
        
        # 7. 测试强平价格计算
        # 多头强平价格 = 开仓价 * (1 - 0.8 / 杠杆)
        btc_liquidation = pos1.entry_price * (1 - 0.8 / pos1.leverage)
        expected_btc_liquidation = 50000.0 * 0.92  # 50000.0 * (1 - 0.8 / 10) = 46000.0
        assert abs(btc_liquidation - 46000.0) < 0.01, f"BTC强平价格计算错误: {btc_liquidation}"

        # 空头强平价格 = 开仓价 * (1 + 0.8 / 杠杆)
        eth_liquidation = pos2.entry_price * (1 + 0.8 / pos2.leverage)
        expected_eth_liquidation = 3000.0 * 1.16  # 3000.0 * (1 + 0.8 / 5) = 3480.0
        assert abs(eth_liquidation - 3480.0) < 0.01, f"ETH强平价格计算错误: {eth_liquidation}"
        
        print("✅ 强平价格计算正确")
        
        # 8. 测试API数据格式转换
        api_positions = []
        for position in positions:
            # 计算强平价格
            liquidation_price = 0.0
            if position.leverage > 1:
                if position.side == TradingSide.LONG:
                    liquidation_price = position.entry_price * (1 - 0.8 / position.leverage)
                else:
                    liquidation_price = position.entry_price * (1 + 0.8 / position.leverage)
            
            api_position = {
                "symbol": position.symbol,
                "side": position.side.value,
                "amount": position.amount,
                "entry_price": position.entry_price,
                "current_price": position.current_price,
                "leverage": position.leverage,
                "unrealized_pnl": position.unrealized_pnl,
                "unrealized_pnl_percentage": position.unrealized_pnl_percentage,
                "margin_used": position.margin_used,
                "liquidation_price": liquidation_price,
                "created_at": datetime.fromtimestamp(position.created_at / 1000).isoformat() + "Z"
            }
            api_positions.append(api_position)
        
        # 验证API格式
        assert len(api_positions) == 2, "API持仓数量不正确"
        assert api_positions[0]["side"] == "long", "BTC持仓方向转换错误"
        assert api_positions[1]["side"] == "short", "ETH持仓方向转换错误"
        assert abs(api_positions[0]["liquidation_price"] - 46000.0) < 0.01, "BTC强平价格API格式错误"
        assert abs(api_positions[1]["liquidation_price"] - 3480.0) < 0.01, "ETH强平价格API格式错误"
        
        print("✅ API数据格式转换正确")
        
        print("\n🎉 持仓API逻辑测试全部通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_positions_api_endpoints_mock():
    """测试持仓API端点（使用模拟数据）"""
    
    print("\n=== 持仓API端点测试（模拟数据）===")
    
    try:
        # 导入API函数
        from src.web.routes.trading import get_positions, get_position_detail
        
        # 1. 测试无协调器情况
        print("1. 测试无协调器情况...")
        set_trading_coordinator(None)
        
        response = await get_positions()
        if hasattr(response, 'body'):
            import json
            data = json.loads(response.body)
            assert data['total_count'] == 0, "无协调器时应返回空数据"
            print("✅ 无协调器情况处理正确")
        
        # 2. 测试有协调器但无持仓情况
        print("2. 测试有协调器但无持仓情况...")
        mock_coordinator = Mock()
        mock_coordinator.current_positions = []
        set_trading_coordinator(mock_coordinator)
        
        response = await get_positions()
        if hasattr(response, 'body'):
            import json
            data = json.loads(response.body)
            assert data['total_count'] == 0, "无持仓时应返回空数据"
            print("✅ 无持仓情况处理正确")
        
        print("✅ 持仓API端点测试完成")
        return True
        
    except Exception as e:
        print(f"❌ API端点测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("开始持仓API逻辑测试...")
    
    # 测试1: API逻辑
    success1 = test_positions_api_logic()
    
    # 测试2: API端点
    success2 = asyncio.run(test_positions_api_endpoints_mock())
    
    if success1 and success2:
        print("\n🎉 所有测试通过！持仓管理API逻辑正确。")
    else:
        print("\n⚠️ 部分测试失败，请检查错误信息。")
