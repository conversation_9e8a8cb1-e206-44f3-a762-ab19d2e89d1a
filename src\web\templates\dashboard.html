{% extends "base.html" %}

{% block title %}仪表板 - DeepSeek量化交易系统{% endblock %}

{% block breadcrumb %}
<li style="display: flex; align-items: center; gap: 4px;">
    <span>/</span>
    <span>📊</span>
    <span>仪表板</span>
</li>
{% endblock %}

{% block extra_css %}
<style>
    /* 仪表板特定样式 */
    .dashboard-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 24px;
        margin-bottom: 24px;
    }
    
    .dashboard-card {
        background: var(--ant-color-bg-container);
        border: 1px solid var(--ant-color-border-secondary);
        border-radius: var(--ant-border-radius);
        padding: 24px;
        box-shadow: var(--ant-box-shadow);
        transition: all 0.3s;
    }
    
    .dashboard-card:hover {
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
    }
    
    .card-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--ant-color-text);
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .card-value {
        font-size: 32px;
        font-weight: 700;
        color: var(--ant-color-primary);
        margin-bottom: 8px;
    }
    
    .card-change {
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 4px;
    }
    
    .card-change.positive {
        color: var(--ant-color-success);
    }
    
    .card-change.negative {
        color: var(--ant-color-error);
    }
    
    .status-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        margin-bottom: 24px;
    }
    
    .status-item {
        background: var(--ant-color-bg-container);
        border: 1px solid var(--ant-color-border-secondary);
        border-radius: var(--ant-border-radius);
        padding: 16px;
        text-align: center;
    }
    
    .status-icon {
        font-size: 24px;
        margin-bottom: 8px;
    }
    
    .status-label {
        font-size: 14px;
        color: var(--ant-color-text-secondary);
        margin-bottom: 4px;
    }
    
    .status-value {
        font-size: 16px;
        font-weight: 600;
        color: var(--ant-color-text);
    }
    
    .activity-log {
        background: var(--ant-color-bg-container);
        border: 1px solid var(--ant-color-border-secondary);
        border-radius: var(--ant-border-radius);
        padding: 24px;
        max-height: 400px;
        overflow-y: auto;
    }
    
    .log-item {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 12px 0;
        border-bottom: 1px solid var(--ant-color-border-secondary);
    }
    
    .log-item:last-child {
        border-bottom: none;
    }
    
    .log-time {
        font-size: 12px;
        color: var(--ant-color-text-tertiary);
        white-space: nowrap;
        min-width: 80px;
    }
    
    .log-content {
        flex: 1;
        font-size: 14px;
        color: var(--ant-color-text);
    }
    
    .log-type {
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        white-space: nowrap;
    }
    
    .log-type.info {
        background: rgba(22, 119, 255, 0.1);
        color: var(--ant-color-info);
    }
    
    .log-type.success {
        background: rgba(82, 196, 26, 0.1);
        color: var(--ant-color-success);
    }
    
    .log-type.warning {
        background: rgba(250, 173, 20, 0.1);
        color: var(--ant-color-warning);
    }
    
    .log-type.error {
        background: rgba(255, 77, 79, 0.1);
        color: var(--ant-color-error);
    }
    
    .refresh-btn {
        background: var(--ant-color-primary);
        color: white;
        border: none;
        border-radius: var(--ant-border-radius);
        padding: 8px 16px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        transition: all 0.3s;
    }
    
    .refresh-btn:hover {
        background: #4096ff;
        transform: translateY(-1px);
    }
    
    .loading {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 40px;
        color: var(--ant-color-text-secondary);
    }
    
    .loading-spinner {
        width: 20px;
        height: 20px;
        border: 2px solid var(--ant-color-border);
        border-top-color: var(--ant-color-primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-right: 8px;
    }
    
    @keyframes spin {
        to { transform: rotate(360deg); }
    }
    
    @media (max-width: 768px) {
        .dashboard-grid {
            grid-template-columns: 1fr;
        }
        
        .status-grid {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .card-value {
            font-size: 24px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div style="margin-bottom: 24px; display: flex; justify-content: space-between; align-items: center;">
    <h1 style="margin: 0; font-size: 24px; font-weight: 600; color: var(--ant-color-text);">
        📊 系统仪表板
    </h1>
    <div style="display: flex; align-items: center; gap: 16px;">
        <span style="font-size: 14px; color: var(--ant-color-text-secondary);">
            最后更新: <span id="lastUpdate">--</span>
        </span>
        <button class="refresh-btn" id="refreshBtn">
            <span>🔄</span>
            <span>刷新数据</span>
        </button>
    </div>
</div>

<!-- 核心指标卡片 -->
<div class="dashboard-grid">
    <div class="dashboard-card">
        <div class="card-title">
            <span>💰</span>
            <span>账户总资产</span>
        </div>
        <div class="card-value" id="totalAssets">--</div>
        <div class="card-change" id="assetsChange">
            <span>📈</span>
            <span>+0.00%</span>
        </div>
    </div>
    
    <div class="dashboard-card">
        <div class="card-title">
            <span>💵</span>
            <span>可用余额</span>
        </div>
        <div class="card-value" id="availableBalance">--</div>
        <div class="card-change" id="balanceChange">
            <span>💳</span>
            <span>可用于交易</span>
        </div>
    </div>
    
    <div class="dashboard-card">
        <div class="card-title">
            <span>📊</span>
            <span>当日盈亏</span>
        </div>
        <div class="card-value" id="dailyPnl">--</div>
        <div class="card-change" id="pnlChange">
            <span>📈</span>
            <span>+0.00%</span>
        </div>
    </div>
    
    <div class="dashboard-card">
        <div class="card-title">
            <span>🎯</span>
            <span>持仓数量</span>
        </div>
        <div class="card-value" id="positionCount">--</div>
        <div class="card-change" id="positionChange">
            <span>📋</span>
            <span>活跃持仓</span>
        </div>
    </div>
</div>

<!-- 系统状态网格 -->
<div class="status-grid">
    <div class="status-item">
        <div class="status-icon">🤖</div>
        <div class="status-label">AI引擎状态</div>
        <div class="status-value" id="aiStatus">运行中</div>
    </div>
    
    <div class="status-item">
        <div class="status-icon">🔗</div>
        <div class="status-label">交易所连接</div>
        <div class="status-value" id="exchangeStatus">已连接</div>
    </div>
    
    <div class="status-item">
        <div class="status-icon">⚡</div>
        <div class="status-label">系统延迟</div>
        <div class="status-value" id="systemLatency">--ms</div>
    </div>
    
    <div class="status-item">
        <div class="status-icon">📡</div>
        <div class="status-label">数据更新</div>
        <div class="status-value" id="dataUpdate">实时</div>
    </div>
    
    <div class="status-item">
        <div class="status-icon">🛡️</div>
        <div class="status-label">风控状态</div>
        <div class="status-value" id="riskStatus">正常</div>
    </div>
    
    <div class="status-item">
        <div class="status-icon">⏱️</div>
        <div class="status-label">运行时长</div>
        <div class="status-value" id="uptime">--</div>
    </div>
</div>

<!-- 活动日志 -->
<div style="margin-top: 24px;">
    <h2 style="margin: 0 0 16px 0; font-size: 18px; font-weight: 600; color: var(--ant-color-text);">
        📝 最近活动
    </h2>
    <div class="activity-log" id="activityLog">
        <div class="loading">
            <div class="loading-spinner"></div>
            <span>加载活动日志...</span>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 仪表板数据管理
    class DashboardManager {
        constructor() {
            this.isLoading = false;
            this.refreshInterval = null;
            this.init();
        }
        
        init() {
            this.loadDashboardData();
            this.startAutoRefresh();
            this.bindEvents();
        }
        
        bindEvents() {
            document.getElementById('refreshBtn').addEventListener('click', () => {
                this.loadDashboardData();
            });
        }
        
        async loadDashboardData() {
            if (this.isLoading) return;
            
            this.isLoading = true;
            this.showLoading();
            
            try {
                // 并行加载所有数据
                const [accountData, systemStatus, activityLogs] = await Promise.all([
                    this.fetchAccountData(),
                    this.fetchSystemStatus(),
                    this.fetchActivityLogs()
                ]);
                
                this.updateAccountMetrics(accountData);
                this.updateSystemStatus(systemStatus);
                this.updateActivityLogs(activityLogs);
                
                // 更新最后更新时间
                document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString('zh-CN');
                
            } catch (error) {
                console.error('加载仪表板数据失败:', error);
                this.showError('数据加载失败，请稍后重试');
            } finally {
                this.isLoading = false;
                this.hideLoading();
            }
        }
        
        async fetchAccountData() {
            const response = await axios.get('/api/dashboard/account');
            return response.data;
        }
        
        async fetchSystemStatus() {
            const response = await axios.get('/api/dashboard/system-status');
            return response.data;
        }
        
        async fetchActivityLogs() {
            const response = await axios.get('/api/dashboard/activity-logs');
            return response.data;
        }
        
        updateAccountMetrics(data) {
            console.log('收到账户数据:', data); // 调试日志

            // 更新账户总资产 - 使用后端返回的字段名
            const totalAssets = data.total_balance_usdt || 0;
            document.getElementById('totalAssets').textContent = this.formatCurrency(totalAssets);

            // 更新可用余额 - 使用后端返回的字段名
            const availableBalance = data.available_balance_usdt || 0;
            document.getElementById('availableBalance').textContent = this.formatCurrency(availableBalance);

            // 更新当日盈亏 - 暂时显示为0，因为后端还没有实现这个字段
            const dailyPnl = data.daily_pnl || 0;
            const pnlElement = document.getElementById('dailyPnl');
            const pnlChangeElement = document.getElementById('pnlChange');

            pnlElement.textContent = this.formatCurrency(dailyPnl);
            pnlElement.style.color = dailyPnl >= 0 ? 'var(--ant-color-success)' : 'var(--ant-color-error)';

            const pnlPercentage = data.daily_pnl_percentage || 0;
            pnlChangeElement.innerHTML = `
                <span>${dailyPnl >= 0 ? '📈' : '📉'}</span>
                <span>${dailyPnl >= 0 ? '+' : ''}${pnlPercentage.toFixed(2)}%</span>
            `;
            pnlChangeElement.className = `card-change ${dailyPnl >= 0 ? 'positive' : 'negative'}`;

            // 更新持仓数量 - 暂时显示为0，因为后端还没有实现这个字段
            document.getElementById('positionCount').textContent = data.position_count || 0;
        }
        
        updateSystemStatus(data) {
            // 更新各种系统状态
            document.getElementById('aiStatus').textContent = data.aiEngineStatus || '未知';
            document.getElementById('exchangeStatus').textContent = data.exchangeConnection || '未知';
            document.getElementById('systemLatency').textContent = `${data.latency || '--'}ms`;
            document.getElementById('dataUpdate').textContent = data.dataUpdateStatus || '未知';
            document.getElementById('riskStatus').textContent = data.riskManagementStatus || '未知';
            document.getElementById('uptime').textContent = this.formatUptime(data.uptime || 0);
        }
        
        updateActivityLogs(logs) {
            const logContainer = document.getElementById('activityLog');
            
            if (!logs || logs.length === 0) {
                logContainer.innerHTML = '<div class="loading">暂无活动记录</div>';
                return;
            }
            
            const logHtml = logs.map(log => `
                <div class="log-item">
                    <div class="log-time">${this.formatTime(log.timestamp)}</div>
                    <div class="log-type ${log.level}">${this.getLogTypeText(log.level)}</div>
                    <div class="log-content">${log.message}</div>
                </div>
            `).join('');
            
            logContainer.innerHTML = logHtml;
        }
        
        formatCurrency(amount) {
            return new Intl.NumberFormat('zh-CN', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(amount);
        }
        
        formatUptime(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
        
        formatTime(timestamp) {
            return new Date(timestamp).toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }
        
        getLogTypeText(level) {
            const types = {
                'info': '信息',
                'success': '成功',
                'warning': '警告',
                'error': '错误'
            };
            return types[level] || '未知';
        }
        
        showLoading() {
            const refreshBtn = document.getElementById('refreshBtn');
            refreshBtn.innerHTML = '<div class="loading-spinner"></div><span>加载中...</span>';
            refreshBtn.disabled = true;
        }
        
        hideLoading() {
            const refreshBtn = document.getElementById('refreshBtn');
            refreshBtn.innerHTML = '<span>🔄</span><span>刷新数据</span>';
            refreshBtn.disabled = false;
        }
        
        showError(message) {
            // 这里可以显示错误提示
            console.error(message);
        }
        
        startAutoRefresh() {
            // 每30秒自动刷新数据
            this.refreshInterval = setInterval(() => {
                this.loadDashboardData();
            }, 30000);
        }
        
        stopAutoRefresh() {
            if (this.refreshInterval) {
                clearInterval(this.refreshInterval);
                this.refreshInterval = null;
            }
        }
    }
    
    // 页面加载完成后初始化仪表板
    document.addEventListener('DOMContentLoaded', () => {
        window.dashboardManager = new DashboardManager();
    });
    
    // 页面卸载时清理定时器
    window.addEventListener('beforeunload', () => {
        if (window.dashboardManager) {
            window.dashboardManager.stopAutoRefresh();
        }
    });
</script>
{% endblock %}
