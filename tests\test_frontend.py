#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
前端功能测试脚本

此脚本测试前端页面的基本功能，包括：
1. 页面加载测试
2. API端点测试
3. JavaScript文件加载测试
4. 静态资源测试
"""

import requests
import time
import json
from pathlib import Path

class FrontendTester:
    def __init__(self, base_url=None):
        # 从配置文件获取正确的端口
        if base_url is None:
            try:
                import sys
                from pathlib import Path
                project_root = Path(__file__).parent.parent
                sys.path.insert(0, str(project_root))

                from config.settings import Settings
                settings = Settings()
                base_url = f"http://{settings.web_host}:{settings.web_port}"
                print(f"从配置文件获取URL: {base_url}")
            except Exception as e:
                print(f"无法加载配置，使用默认URL: {e}")
                base_url = "http://127.0.0.1:8000"

        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
    
    def test_page_load(self, path, expected_status=200):
        """测试页面加载"""
        try:
            url = f"{self.base_url}{path}"
            print(f"测试页面加载: {url}")
            
            response = self.session.get(url, timeout=10)
            success = response.status_code == expected_status
            
            result = {
                "test": f"页面加载 - {path}",
                "url": url,
                "status_code": response.status_code,
                "expected_status": expected_status,
                "success": success,
                "response_time": response.elapsed.total_seconds(),
                "content_length": len(response.content)
            }
            
            if success:
                print(f"✅ 页面加载成功: {path} (状态码: {response.status_code})")
            else:
                print(f"❌ 页面加载失败: {path} (状态码: {response.status_code}, 期望: {expected_status})")
            
            self.test_results.append(result)
            return success
            
        except Exception as e:
            print(f"❌ 页面加载异常: {path} - {str(e)}")
            self.test_results.append({
                "test": f"页面加载 - {path}",
                "url": f"{self.base_url}{path}",
                "success": False,
                "error": str(e)
            })
            return False
    
    def test_api_endpoint(self, path, expected_status=200):
        """测试API端点"""
        try:
            url = f"{self.base_url}{path}"
            print(f"测试API端点: {url}")
            
            response = self.session.get(url, timeout=10)
            success = response.status_code == expected_status
            
            # 尝试解析JSON响应
            json_data = None
            json_keys = None
            try:
                json_data = response.json()
                if isinstance(json_data, dict):
                    json_keys = list(json_data.keys())
                elif isinstance(json_data, list):
                    json_keys = f"列表，包含{len(json_data)}个元素"
            except:
                pass

            result = {
                "test": f"API端点 - {path}",
                "url": url,
                "status_code": response.status_code,
                "expected_status": expected_status,
                "success": success,
                "response_time": response.elapsed.total_seconds(),
                "has_json": json_data is not None,
                "json_keys": json_keys
            }
            
            if success:
                print(f"✅ API端点正常: {path} (状态码: {response.status_code})")
                if json_keys:
                    print(f"   JSON字段: {json_keys}")
            else:
                print(f"❌ API端点异常: {path} (状态码: {response.status_code}, 期望: {expected_status})")
            
            self.test_results.append(result)
            return success
            
        except Exception as e:
            print(f"❌ API端点异常: {path} - {str(e)}")
            self.test_results.append({
                "test": f"API端点 - {path}",
                "url": f"{self.base_url}{path}",
                "success": False,
                "error": str(e)
            })
            return False
    
    def test_static_resource(self, path, expected_status=200):
        """测试静态资源"""
        try:
            url = f"{self.base_url}{path}"
            print(f"测试静态资源: {url}")
            
            response = self.session.get(url, timeout=10)
            success = response.status_code == expected_status
            
            result = {
                "test": f"静态资源 - {path}",
                "url": url,
                "status_code": response.status_code,
                "expected_status": expected_status,
                "success": success,
                "response_time": response.elapsed.total_seconds(),
                "content_type": response.headers.get('content-type', ''),
                "content_length": len(response.content)
            }
            
            if success:
                print(f"✅ 静态资源正常: {path} (类型: {response.headers.get('content-type', 'unknown')})")
            else:
                print(f"❌ 静态资源异常: {path} (状态码: {response.status_code}, 期望: {expected_status})")
            
            self.test_results.append(result)
            return success
            
        except Exception as e:
            print(f"❌ 静态资源异常: {path} - {str(e)}")
            self.test_results.append({
                "test": f"静态资源 - {path}",
                "url": f"{self.base_url}{path}",
                "success": False,
                "error": str(e)
            })
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始前端功能测试...")
        print("=" * 60)
        
        # 测试主要页面
        pages = [
            "/",
            "/positions", 
            "/ai-decisions",
            "/settings",
            "/logs"
        ]
        
        print("\n📄 测试页面加载...")
        for page in pages:
            self.test_page_load(page)
            time.sleep(0.5)  # 避免请求过快
        
        # 测试API端点
        api_endpoints = [
            "/api/dashboard/account",
            "/api/dashboard/system-status", 
            "/api/dashboard/activity-logs",
            "/api/system/status"
        ]
        
        print("\n🔌 测试API端点...")
        for endpoint in api_endpoints:
            self.test_api_endpoint(endpoint)
            time.sleep(0.5)
        
        # 测试静态资源
        static_resources = [
            "/static/antd-theme.css",
            "/static/js/utils.js",
            "/static/js/dashboard.js",
            "/static/js/trading.js"
        ]
        
        print("\n📦 测试静态资源...")
        for resource in static_resources:
            self.test_static_resource(resource)
            time.sleep(0.5)
        
        # 生成测试报告
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📊 测试结果汇总")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result.get('success', False))
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {failed_tests}")
        print(f"成功率: {(passed_tests/total_tests*100):.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result.get('success', False):
                    print(f"  - {result['test']}")
                    if 'error' in result:
                        print(f"    错误: {result['error']}")
                    elif 'status_code' in result:
                        print(f"    状态码: {result['status_code']} (期望: {result.get('expected_status', 200)})")
        
        # 保存详细报告
        report_file = Path("tests/frontend_test_report.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                "timestamp": time.time(),
                "summary": {
                    "total": total_tests,
                    "passed": passed_tests,
                    "failed": failed_tests,
                    "success_rate": passed_tests/total_tests*100
                },
                "results": self.test_results
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细报告已保存到: {report_file}")

def main():
    """主函数"""
    tester = FrontendTester()
    tester.run_all_tests()

if __name__ == "__main__":
    main()
