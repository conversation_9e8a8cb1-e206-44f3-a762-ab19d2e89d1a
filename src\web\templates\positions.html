{% extends "base.html" %}

{% block title %}持仓管理 - DeepSeek量化交易系统{% endblock %}

{% block breadcrumb %}
<li style="display: flex; align-items: center; gap: 4px;">
    <span>/</span>
    <span>📈</span>
    <span>持仓管理</span>
</li>
{% endblock %}

{% block extra_css %}
<style>
    /* 持仓管理特定样式 */
    .positions-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
        flex-wrap: wrap;
        gap: 16px;
    }
    
    .positions-stats {
        display: flex;
        gap: 24px;
        align-items: center;
        flex-wrap: wrap;
    }
    
    .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 12px 16px;
        background: var(--ant-color-bg-container);
        border: 1px solid var(--ant-color-border-secondary);
        border-radius: var(--ant-border-radius);
        min-width: 120px;
    }
    
    .stat-label {
        font-size: 12px;
        color: var(--ant-color-text-secondary);
        margin-bottom: 4px;
    }
    
    .stat-value {
        font-size: 18px;
        font-weight: 600;
        color: var(--ant-color-text);
    }
    
    .stat-value.positive {
        color: var(--ant-color-success);
    }
    
    .stat-value.negative {
        color: var(--ant-color-error);
    }
    
    .positions-table {
        background: var(--ant-color-bg-container);
        border: 1px solid var(--ant-color-border-secondary);
        border-radius: var(--ant-border-radius);
        overflow: hidden;
        box-shadow: var(--ant-box-shadow);
    }
    
    .table-header {
        background: var(--ant-color-fill-secondary);
        padding: 16px 24px;
        border-bottom: 1px solid var(--ant-color-border-secondary);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .table-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--ant-color-text);
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .table-actions {
        display: flex;
        gap: 8px;
    }
    
    .ant-table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .ant-table th,
    .ant-table td {
        padding: 16px;
        text-align: left;
        border-bottom: 1px solid var(--ant-color-border-secondary);
    }
    
    .ant-table th {
        background: var(--ant-color-fill-secondary);
        font-weight: 600;
        color: var(--ant-color-text);
        font-size: 14px;
    }
    
    .ant-table td {
        color: var(--ant-color-text);
        font-size: 14px;
    }
    
    .ant-table tbody tr:hover {
        background: var(--ant-color-fill);
    }
    
    .position-symbol {
        font-weight: 600;
        color: var(--ant-color-text);
    }
    
    .position-side {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        text-transform: uppercase;
    }
    
    .position-side.long {
        background: rgba(82, 196, 26, 0.1);
        color: var(--ant-color-success);
    }
    
    .position-side.short {
        background: rgba(255, 77, 79, 0.1);
        color: var(--ant-color-error);
    }
    
    .pnl-value {
        font-weight: 600;
    }
    
    .pnl-value.positive {
        color: var(--ant-color-success);
    }
    
    .pnl-value.negative {
        color: var(--ant-color-error);
    }
    
    .action-buttons {
        display: flex;
        gap: 8px;
    }
    
    .ant-btn {
        padding: 4px 12px;
        border-radius: 4px;
        border: 1px solid var(--ant-color-border);
        background: var(--ant-color-bg-container);
        color: var(--ant-color-text);
        cursor: pointer;
        font-size: 12px;
        transition: all 0.3s;
    }
    
    .ant-btn:hover {
        border-color: var(--ant-color-primary);
        color: var(--ant-color-primary);
    }
    
    .ant-btn-primary {
        background: var(--ant-color-primary);
        border-color: var(--ant-color-primary);
        color: white;
    }
    
    .ant-btn-primary:hover {
        background: #4096ff;
        border-color: #4096ff;
    }
    
    .ant-btn-danger {
        background: var(--ant-color-error);
        border-color: var(--ant-color-error);
        color: white;
    }
    
    .ant-btn-danger:hover {
        background: #ff7875;
        border-color: #ff7875;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: var(--ant-color-text-secondary);
    }
    
    .empty-icon {
        font-size: 48px;
        margin-bottom: 16px;
        opacity: 0.5;
    }
    
    .empty-text {
        font-size: 16px;
        margin-bottom: 8px;
    }
    
    .empty-description {
        font-size: 14px;
        color: var(--ant-color-text-tertiary);
    }
    
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
    }
    
    [data-theme="dark"] .loading-overlay {
        background: rgba(0, 0, 0, 0.8);
    }
    
    .loading-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;
    }
    
    .loading-spinner {
        width: 32px;
        height: 32px;
        border: 3px solid var(--ant-color-border);
        border-top-color: var(--ant-color-primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        to { transform: rotate(360deg); }
    }
    
    @media (max-width: 768px) {
        .positions-header {
            flex-direction: column;
            align-items: stretch;
        }
        
        .positions-stats {
            justify-content: space-between;
        }
        
        .stat-item {
            min-width: auto;
            flex: 1;
        }
        
        .ant-table {
            font-size: 12px;
        }
        
        .ant-table th,
        .ant-table td {
            padding: 12px 8px;
        }
        
        .action-buttons {
            flex-direction: column;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="positions-header">
    <div>
        <h1 style="margin: 0 0 8px 0; font-size: 24px; font-weight: 600; color: var(--ant-color-text);">
            📈 持仓管理
        </h1>
        <p style="margin: 0; color: var(--ant-color-text-secondary);">
            管理和监控当前的交易持仓
        </p>
    </div>
    
    <div class="positions-stats">
        <div class="stat-item">
            <div class="stat-label">持仓数量</div>
            <div class="stat-value" id="totalPositions">--</div>
        </div>
        <div class="stat-item">
            <div class="stat-label">总浮动盈亏</div>
            <div class="stat-value" id="totalUnrealizedPnl">--</div>
        </div>
        <div class="stat-item">
            <div class="stat-label">已用保证金</div>
            <div class="stat-value" id="totalMargin">--</div>
        </div>
        <div class="stat-item">
            <div class="stat-label">平均收益率</div>
            <div class="stat-value" id="avgReturnRate">--</div>
        </div>
    </div>
</div>

<div class="positions-table" style="position: relative;">
    <div class="table-header">
        <div class="table-title">
            <span>📋</span>
            <span>当前持仓</span>
        </div>
        <div class="table-actions">
            <button class="ant-btn" id="refreshPositions">
                <span>🔄</span>
                <span>刷新</span>
            </button>
            <button class="ant-btn ant-btn-danger" id="closeAllPositions">
                <span>❌</span>
                <span>全部平仓</span>
            </button>
        </div>
    </div>
    
    <div id="positionsTableContainer">
        <table class="ant-table">
            <thead>
                <tr>
                    <th>交易对</th>
                    <th>方向</th>
                    <th>数量</th>
                    <th>开仓价格</th>
                    <th>当前价格</th>
                    <th>杠杆</th>
                    <th>浮动盈亏</th>
                    <th>收益率</th>
                    <th>保证金</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="positionsTableBody">
                <!-- 持仓数据将通过JavaScript动态加载 -->
            </tbody>
        </table>
    </div>
    
    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div>加载持仓数据...</div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 持仓管理器
    class PositionsManager {
        constructor() {
            this.positions = [];
            this.isLoading = false;
            this.refreshInterval = null;
            this.init();
        }
        
        init() {
            this.loadPositions();
            this.startAutoRefresh();
            this.bindEvents();
        }
        
        bindEvents() {
            // 刷新按钮
            document.getElementById('refreshPositions').addEventListener('click', () => {
                this.loadPositions();
            });
            
            // 全部平仓按钮
            document.getElementById('closeAllPositions').addEventListener('click', () => {
                this.closeAllPositions();
            });
        }
        
        async loadPositions() {
            if (this.isLoading) return;
            
            this.isLoading = true;
            this.showLoading();
            
            try {
                const response = await axios.get('/api/trading/positions');
                this.positions = response.data.positions || [];
                
                this.updatePositionsStats(response.data.stats);
                this.renderPositionsTable();
                
            } catch (error) {
                console.error('加载持仓数据失败:', error);
                this.showError('加载持仓数据失败，请稍后重试');
            } finally {
                this.isLoading = false;
                this.hideLoading();
            }
        }
        
        updatePositionsStats(stats) {
            document.getElementById('totalPositions').textContent = stats?.totalPositions || 0;
            
            const totalPnl = stats?.totalUnrealizedPnl || 0;
            const pnlElement = document.getElementById('totalUnrealizedPnl');
            pnlElement.textContent = this.formatCurrency(totalPnl);
            pnlElement.className = `stat-value ${totalPnl >= 0 ? 'positive' : 'negative'}`;
            
            document.getElementById('totalMargin').textContent = this.formatCurrency(stats?.totalMargin || 0);
            
            const avgReturn = stats?.avgReturnRate || 0;
            const returnElement = document.getElementById('avgReturnRate');
            returnElement.textContent = `${avgReturn >= 0 ? '+' : ''}${avgReturn.toFixed(2)}%`;
            returnElement.className = `stat-value ${avgReturn >= 0 ? 'positive' : 'negative'}`;
        }
        
        renderPositionsTable() {
            const tbody = document.getElementById('positionsTableBody');
            
            if (this.positions.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="10">
                            <div class="empty-state">
                                <div class="empty-icon">📭</div>
                                <div class="empty-text">暂无持仓</div>
                                <div class="empty-description">当前没有活跃的交易持仓</div>
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }
            
            const rows = this.positions.map(position => {
                const pnl = position.unrealized_pnl || 0;
                const pnlPercentage = position.unrealized_pnl_percentage || 0;

                return `
                    <tr>
                        <td>
                            <div class="position-symbol">${position.symbol}</div>
                        </td>
                        <td>
                            <span class="position-side ${position.side}">
                                ${position.side === 'long' ? '做多' : '做空'}
                            </span>
                        </td>
                        <td>${position.amount}</td>
                        <td>${this.formatPrice(position.entry_price)}</td>
                        <td>${this.formatPrice(position.current_price)}</td>
                        <td>${position.leverage}x</td>
                        <td>
                            <div class="pnl-value ${pnl >= 0 ? 'positive' : 'negative'}">
                                ${this.formatCurrency(pnl)}
                            </div>
                        </td>
                        <td>
                            <div class="pnl-value ${pnlPercentage >= 0 ? 'positive' : 'negative'}">
                                ${pnlPercentage >= 0 ? '+' : ''}${pnlPercentage.toFixed(2)}%
                            </div>
                        </td>
                        <td>${this.formatCurrency(position.margin_used)}</td>
                        <td>
                            <div class="action-buttons">
                                <button class="ant-btn" onclick="positionsManager.adjustPosition('${position.symbol}')">
                                    调整
                                </button>
                                <button class="ant-btn ant-btn-danger" onclick="positionsManager.closePosition('${position.symbol}')">
                                    平仓
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
            
            tbody.innerHTML = rows;
        }
        
        async closePosition(symbol) {
            if (!confirm(`确定要平仓 ${symbol} 吗？`)) {
                return;
            }
            
            try {
                await axios.post(`/api/trading/positions/${symbol}/close`);
                alert('平仓请求已提交');
                this.loadPositions(); // 重新加载数据
            } catch (error) {
                console.error('平仓失败:', error);
                alert('平仓失败: ' + (error.response?.data?.message || error.message));
            }
        }
        
        async closeAllPositions() {
            if (!confirm('确定要平掉所有持仓吗？这个操作不可撤销！')) {
                return;
            }
            
            try {
                await axios.post('/api/trading/positions/close-all');
                alert('全部平仓请求已提交');
                this.loadPositions(); // 重新加载数据
            } catch (error) {
                console.error('全部平仓失败:', error);
                alert('全部平仓失败: ' + (error.response?.data?.message || error.message));
            }
        }
        
        adjustPosition(symbol) {
            // 这里可以打开调整持仓的模态框
            alert(`调整持仓功能开发中: ${symbol}`);
        }
        
        formatCurrency(amount) {
            return new Intl.NumberFormat('zh-CN', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(amount);
        }
        
        formatPrice(price) {
            return new Intl.NumberFormat('zh-CN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 6
            }).format(price);
        }
        
        showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
        }
        
        hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }
        
        showError(message) {
            console.error(message);
            // 这里可以显示错误提示
        }
        
        startAutoRefresh() {
            // 每15秒自动刷新持仓数据
            this.refreshInterval = setInterval(() => {
                this.loadPositions();
            }, 15000);
        }
        
        stopAutoRefresh() {
            if (this.refreshInterval) {
                clearInterval(this.refreshInterval);
                this.refreshInterval = null;
            }
        }
    }
    
    // 页面加载完成后初始化持仓管理器
    document.addEventListener('DOMContentLoaded', () => {
        window.positionsManager = new PositionsManager();
    });
    
    // 页面卸载时清理定时器
    window.addEventListener('beforeunload', () => {
        if (window.positionsManager) {
            window.positionsManager.stopAutoRefresh();
        }
    });
</script>
{% endblock %}
