#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek加密货币永续合约全自动量化系统
主程序入口文件

此文件负责初始化和启动整个交易系统，包括：
1. 环境配置加载
2. 数据库初始化
3. 核心组件启动
4. Web服务启动
5. 系统监控启动
"""

import asyncio
import sys
import signal
import os
import socket
import psutil
from pathlib import Path
from typing import Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入配置和日志
from config.settings import Settings
from config.logging_config import setup_logging
from src.utils.logger import get_logger

# 导入核心组件
from src.data.database_manager import DatabaseManager
from src.core.trading_coordinator import TradingCoordinator, TradingConfig
from src.services.monitoring_service import MonitoringService
from src.core.exchange_client import ExchangeClient
from src.ai.deepseek_client import DeepSeekConfig
from src.services.config_service import ConfigService


logger = get_logger(__name__)


def check_port_in_use(port: int) -> bool:
    """检查端口是否被占用

    Args:
        port: 要检查的端口号

    Returns:
        bool: 端口是否被占用
    """
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.settimeout(1)
            result = sock.connect_ex(('127.0.0.1', port))
            return result == 0
    except Exception:
        return False


def kill_process_using_port(port: int) -> bool:
    """杀死占用指定端口的进程

    Args:
        port: 要释放的端口号

    Returns:
        bool: 是否成功杀死进程
    """
    try:
        killed_any = False
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                # 获取进程的网络连接
                connections = proc.net_connections()
                if connections:
                    for conn in connections:
                        if (hasattr(conn, 'laddr') and
                            conn.laddr and
                            conn.laddr.port == port):
                            logger.info(f"发现占用端口 {port} 的进程: PID={proc.info['pid']}, 名称={proc.info['name']}")
                            proc.kill()
                            proc.wait(timeout=3)
                            logger.info(f"已杀死进程 PID={proc.info['pid']}")
                            killed_any = True
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
            except Exception as e:
                logger.debug(f"检查进程 {proc.info.get('pid', 'unknown')} 时出错: {e}")
                continue

        return killed_any
    except Exception as e:
        logger.error(f"杀死占用端口 {port} 的进程时出错: {e}")
        return False


def cleanup_database_locks() -> bool:
    """清理数据库锁定文件

    Returns:
        bool: 是否成功清理
    """
    try:
        db_path = Path("data/trading_system.db")
        lock_files = [
            db_path.with_suffix('.db-wal'),
            db_path.with_suffix('.db-shm'),
            db_path.parent / f"{db_path.name}-journal"
        ]

        cleaned_any = False
        for lock_file in lock_files:
            if lock_file.exists():
                try:
                    lock_file.unlink()
                    logger.info(f"已删除数据库锁定文件: {lock_file}")
                    cleaned_any = True
                except Exception as e:
                    logger.warning(f"无法删除锁定文件 {lock_file}: {e}")

        if not cleaned_any:
            logger.debug("未发现数据库锁定文件")

        return True
    except Exception as e:
        logger.error(f"清理数据库锁定文件时出错: {e}")
        return False


def ensure_port_available(port: int, max_retries: int = 3) -> bool:
    """确保端口可用，如果被占用则尝试释放

    Args:
        port: 要确保可用的端口号
        max_retries: 最大重试次数

    Returns:
        bool: 端口是否可用
    """
    for attempt in range(max_retries):
        if not check_port_in_use(port):
            logger.info(f"端口 {port} 可用")
            return True

        logger.warning(f"端口 {port} 被占用，尝试释放... (第 {attempt + 1}/{max_retries} 次)")

        if kill_process_using_port(port):
            # 等待一下让进程完全退出
            import time
            time.sleep(2)

            # 再次检查
            if not check_port_in_use(port):
                logger.info(f"端口 {port} 已成功释放")
                return True
        else:
            logger.warning(f"未找到占用端口 {port} 的进程")

    logger.error(f"无法释放端口 {port}，已尝试 {max_retries} 次")
    return False


class TradingSystemManager:
    """交易系统管理器
    
    负责管理整个交易系统的生命周期，包括启动、运行和关闭。
    """
    
    def __init__(self):
        """初始化交易系统管理器"""
        self.settings: Optional[Settings] = None
        self.is_running = False
        self.shutdown_event = asyncio.Event()

        # 核心组件
        self.database_manager: Optional[DatabaseManager] = None
        self.trading_coordinator: Optional[TradingCoordinator] = None
        self.monitoring_service: Optional[MonitoringService] = None
        self.web_app = None
        
    async def initialize(self) -> bool:
        """初始化系统组件
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            logger.info("开始初始化DeepSeek量化交易系统...")
            
            # 1. 加载配置
            self.settings = Settings()
            logger.info("配置加载完成")
            
            # 2. 设置日志系统
            setup_logging(self.settings)
            logger.info("日志系统初始化完成")
            
            # 3. 初始化数据库
            self.database_manager = DatabaseManager()
            logger.info("数据库初始化完成")
            
            # 4. 初始化交易协调器
            config_service = ConfigService()
            exchange_config = config_service.get_exchange_config()

            if exchange_config:
                risk_params = config_service.get_risk_parameters()
                symbols = config_service.get_selected_symbols()
                ai_config_model = self.database_manager.load_ai_config()

                if not ai_config_model:
                    raise Exception("AI配置未找到，请在数据库中设置。")

                deepseek_config = DeepSeekConfig(api_key=ai_config_model.deepseek_api_key)

                trading_config = TradingConfig(
                    symbols=symbols,
                    timeframes=['1h', '15m', '5m'],
                    analysis_interval=ai_config_model.analysis_interval,
                    max_positions=5
                )

                exchange_client = ExchangeClient(exchange_config)

                self.trading_coordinator = TradingCoordinator(
                    exchange_client=exchange_client,
                    deepseek_config=deepseek_config,
                    risk_params=risk_params,
                    trading_config=trading_config
                )
                logger.info("交易协调器初始化完成")
            else:
                self.trading_coordinator = None
                logger.warning("未配置交易所信息，交易协调器未初始化")
            
            # 5. 初始化监控服务
            self.monitoring_service = MonitoringService()
            self.monitoring_service.start_monitoring()
            logger.info("监控服务初始化完成")
            
            # 6. 创建Web应用
            from src.web.app import app
            self.web_app = app

            # 7. 设置Web API的交易协调器引用
            if self.trading_coordinator:
                from src.web.routes.trading import set_trading_coordinator
                set_trading_coordinator(self.trading_coordinator)
                logger.info("Web API交易协调器引用设置完成")

            logger.info("Web应用初始化完成")
            
            logger.info("系统初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"系统初始化失败: {e}")
            return False
    
    async def start(self) -> None:
        """启动交易系统"""
        try:
            logger.info("启动交易系统...")
            
            # 启动核心组件
            if self.trading_coordinator:
                await self.trading_coordinator.start()
                logger.info("交易协调器已启动")
            
            self.is_running = True
            logger.info("交易系统启动成功")
            
            # 等待关闭信号
            await self.shutdown_event.wait()
            
        except Exception as e:
            logger.error(f"交易系统启动失败: {e}")
            raise
    
    async def shutdown(self) -> None:
        """关闭交易系统"""
        try:
            logger.info("开始关闭交易系统...")
            
            self.is_running = False
            
            # 关闭核心组件
            if self.trading_coordinator:
                await self.trading_coordinator.stop()
                logger.info("交易协调器已停止")

            if self.monitoring_service:
                self.monitoring_service.stop_monitoring()
                logger.info("监控服务已停止")
            
            logger.info("交易系统关闭完成")
            
        except Exception as e:
            logger.error(f"关闭交易系统时发生错误: {e}")
        finally:
            self.shutdown_event.set()
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"接收到信号 {signum}，开始关闭系统...")
        asyncio.create_task(self.shutdown())


async def start_web_server(manager: TradingSystemManager):
    """启动Web服务器

    Args:
        manager: 交易系统管理器实例
    """
    try:
        import uvicorn

        # 启动内嵌的Web服务器
        from src.web.app import app

        # 检查是否为开发环境
        is_development = manager.settings.environment.lower() == "development"
        # 热重载需要同时满足：开发环境 + 热重载开关 + 调试模式
        enable_reload = is_development and manager.settings.hot_reload and manager.settings.debug_mode

        config = uvicorn.Config(
            app="src.web.app:app",  # 使用字符串路径而不是app对象，这样热重载才能正常工作
            host=manager.settings.web_host,
            port=manager.settings.web_port,
            log_level=manager.settings.log_level.lower(),
            # 根据配置启用热重载
            reload=enable_reload,
            # 指定要监控的目录
            reload_dirs=["src", "config"] if enable_reload else None,
            # 指定要监控的文件类型
            reload_includes=["*.py"] if enable_reload else None,
            # 排除不需要监控的目录
            reload_excludes=["logs", "data", "__pycache__", "*.pyc", "tests"] if enable_reload else None
        )
        server = uvicorn.Server(config)

        # 显示启动信息
        if enable_reload:
            logger.info(f"Web服务器启动在 http://{manager.settings.web_host}:{manager.settings.web_port} (开发模式 - 热重载已启用)")
            logger.info("监控目录: src, config")
            logger.info("文件变化时将自动重载服务器")
            logger.info("💡 提示: 修改Python文件后无需手动重启服务器")
        else:
            if is_development:
                if not manager.settings.hot_reload:
                    mode = "开发模式 (热重载已禁用 - HOT_RELOAD=false)"
                elif not manager.settings.debug_mode:
                    mode = "开发模式 (热重载已禁用 - DEBUG_MODE=false)"
                else:
                    mode = "开发模式 (热重载已禁用)"
            else:
                mode = "生产模式"
            logger.info(f"Web服务器启动在 http://{manager.settings.web_host}:{manager.settings.web_port} ({mode})")

        # 在后台启动服务器
        await server.serve()

    except Exception as e:
        logger.error(f"Web服务器启动失败: {e}")


async def main():
    """主函数"""
    # 创建交易系统管理器
    manager = TradingSystemManager()
    
    # 设置信号处理器
    signal.signal(signal.SIGINT, manager.signal_handler)
    signal.signal(signal.SIGTERM, manager.signal_handler)
    
    try:
        # 清理数据库锁定文件
        logger.info("清理数据库锁定文件...")
        cleanup_database_locks()

        # # 检查并释放Web服务器端口 (Temporarily disabled for debugging)
        # web_port = manager.settings.web_port if manager.settings else 8000
        # logger.info(f"检查Web服务器端口 {web_port} 是否可用...")
        #
        # if not ensure_port_available(web_port):
        #     logger.error(f"无法释放端口 {web_port}，请手动检查并关闭占用该端口的程序")
        #     sys.exit(1)

        # 初始化系统
        if not await manager.initialize():
            logger.error("系统初始化失败，退出程序")
            sys.exit(1)

        # 创建任务
        tasks = [
            asyncio.create_task(manager.start(), name="trading_system"),
            asyncio.create_task(start_web_server(manager), name="web_server")
        ]
        
        # 等待任务完成
        await asyncio.gather(*tasks, return_exceptions=True)
        
    except KeyboardInterrupt:
        logger.info("接收到键盘中断信号")
    except Exception as e:
        logger.error(f"系统运行时发生错误: {e}")
    finally:
        await manager.shutdown()


if __name__ == "__main__":
    """程序入口点"""
    try:
        # 检查Python版本
        if sys.version_info < (3, 8):
            print("错误：此程序需要Python 3.8或更高版本")
            sys.exit(1)
        
        # 创建必要的目录
        os.makedirs("logs", exist_ok=True)
        os.makedirs("data", exist_ok=True)
        
        # 运行主程序
        asyncio.run(main())
        
    except Exception as e:
        print(f"程序启动失败: {e}")
        sys.exit(1)