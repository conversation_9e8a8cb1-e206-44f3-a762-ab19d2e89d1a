<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}DeepSeek量化交易系统{% endblock %}</title>
    
    <!-- Ant Design 主题样式 -->
    <link rel="stylesheet" href="{{ url_for('static', path='/antd-theme.css') }}">

    <!-- 自定义样式 -->
    <style>
        /* 页面特定样式 */
        .ant-layout {
            min-height: 100vh;
            background-color: var(--ant-color-bg-layout);
            display: flex;
            flex-direction: column;
        }

        .ant-layout-header {
            background: var(--ant-color-bg-container);
            border-bottom: 1px solid var(--ant-color-border-secondary);
            padding: 0 var(--ant-padding-lg);
            height: 64px;
            line-height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: var(--ant-box-shadow-tertiary);
        }

        .ant-layout-sider {
            background: var(--ant-color-bg-container);
            border-right: 1px solid var(--ant-color-border-secondary);
        }

        .ant-layout-content {
            background: var(--ant-color-bg-layout);
            padding: var(--ant-padding-lg);
            margin: 0;
            flex: 1;
            overflow-y: auto;
        }

        .ant-layout-footer {
            background: var(--ant-color-bg-container);
            border-top: 1px solid var(--ant-color-border-secondary);
            text-align: center;
            color: var(--ant-color-text-secondary);
            padding: var(--ant-padding) var(--ant-padding-lg);
        }

        /* 系统Logo */
        .system-logo {
            display: flex;
            align-items: center;
            gap: var(--ant-margin-sm);
            color: var(--ant-color-text);
            text-decoration: none;
            font-size: var(--ant-font-size-lg);
            font-weight: 600;
            transition: color var(--ant-motion-duration-mid);
        }

        .system-logo:hover {
            color: var(--ant-color-primary);
        }

        /* 系统状态指示器 */
        .system-status {
            display: flex;
            align-items: center;
            gap: var(--ant-margin-xs);
            margin-left: var(--ant-margin-lg);
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: var(--ant-color-success);
            animation: pulse 2s infinite;
        }

        .status-indicator.warning {
            background-color: var(--ant-color-warning);
        }

        .status-indicator.error {
            background-color: var(--ant-color-error);
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* 头部右侧信息 */
        .header-right {
            display: flex;
            align-items: center;
            gap: var(--ant-margin-lg);
        }

        .header-info {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            font-size: var(--ant-font-size-xs);
            color: var(--ant-color-text-secondary);
        }

        /* 主题切换按钮 */
        .theme-toggle {
            background: none;
            border: 1px solid var(--ant-color-border);
            border-radius: var(--ant-border-radius);
            padding: var(--ant-padding-xs);
            cursor: pointer;
            color: var(--ant-color-text);
            transition: all var(--ant-motion-duration-mid);
        }

        .theme-toggle:hover {
            border-color: var(--ant-color-primary);
            color: var(--ant-color-primary);
        }

        /* 导航菜单 */
        .ant-menu {
            background: transparent;
            border: none;
        }

        .ant-menu-item {
            display: flex;
            align-items: center;
            gap: var(--ant-margin-sm);
            margin: var(--ant-margin-xs) 0;
            border-radius: var(--ant-border-radius);
            transition: background-color var(--ant-motion-duration-mid);
        }

        .ant-menu-item-selected {
            background-color: var(--ant-color-primary);
            color: white;
        }

        .ant-menu-item:hover {
            background-color: var(--ant-color-fill);
        }

        /* 快速操作区 */
        .quick-actions {
            padding: var(--ant-padding);
            border-top: 1px solid var(--ant-color-border-secondary);
        }

        .quick-actions-title {
            font-size: var(--ant-font-size);
            font-weight: 600;
            color: var(--ant-color-text);
            margin-bottom: var(--ant-margin-sm);
        }

        .quick-actions .ant-btn {
            width: 100%;
            margin-bottom: var(--ant-margin-xs);
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: var(--ant-margin-xs);
        }

        /* 面包屑导航 */
        .ant-breadcrumb {
            margin-bottom: var(--ant-margin-lg);
        }

        /* 页面内容区域 */
        .page-content {
            background: var(--ant-color-bg-container);
            border-radius: var(--ant-border-radius);
            padding: var(--ant-padding-lg);
            box-shadow: var(--ant-box-shadow);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .ant-layout-header {
                padding: 0 var(--ant-padding);
            }

            .ant-layout-content {
                padding: var(--ant-padding);
            }

            .header-right {
                gap: var(--ant-margin-sm);
            }

            .system-logo span {
                display: none;
            }
        }
    </style>
    
    <!-- 额外样式 -->
    {% block extra_css %}{% endblock %}
</head>
<body data-theme="light">
    <!-- Ant Design布局容器 -->
    <div class="ant-layout">
        <!-- 顶部导航栏 -->
        <header class="ant-layout-header">
            <div style="display: flex; align-items: center;">
                <a href="/" class="system-logo">
                    <span class="anticon anticon-robot" style="font-size: 24px;">🤖</span>
                    <span>DeepSeek量化交易系统</span>
                </a>
                <div class="system-status">
                    <span class="status-indicator" id="statusIndicator"></span>
                    <span class="status-text" id="statusText">系统状态检查中...</span>
                </div>
            </div>
            <div class="header-right">
                <button class="theme-toggle" id="themeToggle" title="切换主题">
                    <span id="themeIcon">🌙</span>
                </button>
                <div class="header-info">
                    <div class="current-time" id="currentTime"></div>
                    <div class="system-uptime" id="uptime">运行时间: --:--:--</div>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <div class="ant-layout" style="flex-direction: row; flex: 1;">
            <!-- 侧边导航栏 -->
            <aside class="ant-layout-sider" style="width: 256px; min-width: 256px;">
                <div style="height: 100%; display: flex; flex-direction: column;">
                    <!-- 导航菜单 -->
                    <nav style="flex: 1; padding: 16px 0;">
                        <ul class="ant-menu ant-menu-inline" style="list-style: none; padding: 0; margin: 0;">
                            <li class="ant-menu-item" data-page="dashboard">
                                <a href="/" style="text-decoration: none; color: inherit; display: flex; align-items: center; gap: 12px; padding: 12px 24px;">
                                    <span>📊</span>
                                    <span>仪表板</span>
                                </a>
                            </li>
                            <li class="ant-menu-item" data-page="positions">
                                <a href="/positions" style="text-decoration: none; color: inherit; display: flex; align-items: center; gap: 12px; padding: 12px 24px;">
                                    <span>📈</span>
                                    <span>持仓管理</span>
                                </a>
                            </li>
                            <li class="ant-menu-item" data-page="ai-decisions">
                                <a href="/ai-decisions" style="text-decoration: none; color: inherit; display: flex; align-items: center; gap: 12px; padding: 12px 24px;">
                                    <span>🧠</span>
                                    <span>AI决策</span>
                                </a>
                            </li>
                            <li class="ant-menu-item" data-page="settings">
                                <a href="/settings" style="text-decoration: none; color: inherit; display: flex; align-items: center; gap: 12px; padding: 12px 24px;">
                                    <span>⚙️</span>
                                    <span>系统设置</span>
                                </a>
                            </li>
                            <li class="ant-menu-item" data-page="logs">
                                <a href="/logs" style="text-decoration: none; color: inherit; display: flex; align-items: center; gap: 12px; padding: 12px 24px;">
                                    <span>📝</span>
                                    <span>系统日志</span>
                                </a>
                            </li>
                        </ul>
                    </nav>

                    <!-- 快速操作区 -->
                    <div class="quick-actions">
                        <div class="quick-actions-title">快速操作</div>
                        <button class="ant-btn ant-btn-primary" id="startTradingBtn" style="width: 100%; margin-bottom: 8px; display: flex; align-items: center; justify-content: center; gap: 8px; padding: 8px 16px; border: none; border-radius: 6px; background: var(--ant-color-primary); color: white; cursor: pointer;">
                            <span>▶️</span>
                            <span>启动交易</span>
                        </button>
                        <button class="ant-btn ant-btn-danger" id="stopTradingBtn" style="width: 100%; margin-bottom: 8px; display: flex; align-items: center; justify-content: center; gap: 8px; padding: 8px 16px; border: none; border-radius: 6px; background: var(--ant-color-error); color: white; cursor: pointer;">
                            <span>⏹️</span>
                            <span>停止交易</span>
                        </button>
                        <button class="ant-btn ant-btn-warning" id="emergencyStopBtn" style="width: 100%; margin-bottom: 8px; display: flex; align-items: center; justify-content: center; gap: 8px; padding: 8px 16px; border: none; border-radius: 6px; background: var(--ant-color-warning); color: white; cursor: pointer;">
                            <span>⚠️</span>
                            <span>紧急停止</span>
                        </button>
                    </div>
                </div>
            </aside>

            <!-- 主内容区 -->
            <main class="ant-layout-content">
                <!-- 面包屑导航 -->
                <nav class="ant-breadcrumb" style="margin-bottom: 24px;">
                    <ol style="display: flex; align-items: center; gap: 8px; list-style: none; padding: 0; margin: 0; font-size: 14px; color: var(--ant-color-text-secondary);">
                        <li style="display: flex; align-items: center; gap: 4px;">
                            <span>🏠</span>
                            <span>首页</span>
                        </li>
                        {% block breadcrumb %}{% endblock %}
                    </ol>
                </nav>

                <!-- 页面内容 -->
                <div class="page-content">
                    {% block content %}{% endblock %}
                </div>
            </main>
        </div>

        <!-- 底部状态栏 -->
        <footer class="ant-layout-footer">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div style="display: flex; gap: 24px;">
                    <span>
                        <span>🖥️</span>
                        服务器状态: <span id="serverStatus" class="text-success">正常</span>
                    </span>
                    <span>
                        <span>📡</span>
                        连接状态: <span id="connectionStatus" class="text-success">已连接</span>
                    </span>
                </div>
                <div style="display: flex; gap: 24px;">
                    <span>最后更新: <span id="lastUpdate">--</span></span>
                    <span>版本: v1.0.0</span>
                </div>
            </div>
        </footer>
    </div>

    <!-- 基础JavaScript库 -->
    <script src="https://cdn.jsdelivr.net/npm/axios@1.6.2/dist/axios.min.js"></script>

    <!-- 工具函数库 -->
    <script src="{{ url_for('static', path='/js/utils.js') }}"></script>

    <!-- 页面特定脚本 -->
    {% block extra_js %}{% endblock %}
    
    <!-- 基础功能脚本 -->
    <script>
        // 主题切换功能
        const themeToggle = document.getElementById('themeToggle');
        const themeIcon = document.getElementById('themeIcon');
        const body = document.body;
        
        // 从localStorage获取保存的主题
        const savedTheme = localStorage.getItem('theme') || 'light';
        body.setAttribute('data-theme', savedTheme);
        themeIcon.textContent = savedTheme === 'dark' ? '☀️' : '🌙';
        
        themeToggle.addEventListener('click', () => {
            const currentTheme = body.getAttribute('data-theme');
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            
            body.setAttribute('data-theme', newTheme);
            themeIcon.textContent = newTheme === 'dark' ? '☀️' : '🌙';
            localStorage.setItem('theme', newTheme);
        });
        
        // 当前时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('currentTime').textContent = timeString;
        }
        
        // 每秒更新时间
        setInterval(updateTime, 1000);
        updateTime();
        
        // 导航菜单高亮
        function highlightCurrentPage() {
            const currentPath = window.location.pathname;
            const menuItems = document.querySelectorAll('.ant-menu-item');
            
            menuItems.forEach(item => {
                const link = item.querySelector('a');
                if (link && link.getAttribute('href') === currentPath) {
                    item.classList.add('ant-menu-item-selected');
                } else {
                    item.classList.remove('ant-menu-item-selected');
                }
            });
        }
        
        // 页面加载完成后高亮当前页面
        document.addEventListener('DOMContentLoaded', highlightCurrentPage);
        
        // 系统状态检查
        async function checkSystemStatus() {
            try {
                const response = await axios.get('/api/system/status');
                const status = response.data.status;
                const statusIndicator = document.getElementById('statusIndicator');
                const statusText = document.getElementById('statusText');
                
                if (status === 'running') {
                    statusIndicator.className = 'status-indicator';
                    statusText.textContent = '系统运行中';
                } else if (status === 'warning') {
                    statusIndicator.className = 'status-indicator warning';
                    statusText.textContent = '系统警告';
                } else {
                    statusIndicator.className = 'status-indicator error';
                    statusText.textContent = '系统异常';
                }
            } catch (error) {
                const statusIndicator = document.getElementById('statusIndicator');
                const statusText = document.getElementById('statusText');
                statusIndicator.className = 'status-indicator error';
                statusText.textContent = '连接失败';
            }
        }
        
        // 每30秒检查一次系统状态
        setInterval(checkSystemStatus, 30000);
        checkSystemStatus();
        
        // 快速操作按钮事件
        document.getElementById('startTradingBtn').addEventListener('click', async () => {
            try {
                await axios.post('/api/system/start');
                alert('交易系统启动成功');
            } catch (error) {
                alert('启动失败: ' + error.message);
            }
        });
        
        document.getElementById('stopTradingBtn').addEventListener('click', async () => {
            try {
                await axios.post('/api/system/stop');
                alert('交易系统停止成功');
            } catch (error) {
                alert('停止失败: ' + error.message);
            }
        });
        
        document.getElementById('emergencyStopBtn').addEventListener('click', async () => {
            if (confirm('确定要紧急停止交易系统吗？这将立即停止所有交易活动。')) {
                try {
                    await axios.post('/api/system/emergency-stop');
                    alert('紧急停止成功');
                } catch (error) {
                    alert('紧急停止失败: ' + error.message);
                }
            }
        });
    </script>
</body>
</html>
